import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tattooerp20260622/models/index.dart';

var authToken;
Color spcl = Color(0xff655586);
var currency;

class Global {
  static SharedPreferences? _prefs;
  static String? token;
  static Profile profile = Profile();

  // 是否为release版
  static bool get isRelease => bool.fromEnvironment("dart.vm.product");

  //初始化全局信息，会在APP启动时执行
  static Future init() async {
    _prefs = await SharedPreferences.getInstance();
    var profile = _prefs?.getString("profile");
    token = _prefs?.getString("token");

    if (profile != null) {
      try {
        profile = Profile.fromJson(jsonDecode(profile)) as String?;
      } catch (e) {
        print(e);
      }
    }
  }

  // 持久化Profile信息
  static saveProfile() =>
      _prefs?.setString("profile", jsonEncode(profile.toJson()));
}

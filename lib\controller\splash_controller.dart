import 'package:get/get.dart';

class SplashController extends GetxController implements GetxService {
  DateTime _currentTime = DateTime.now();

  DateTime get currentTime => _currentTime;
  bool _firstTimeConnectionCheck = true;
  bool get firstTimeConnectionCheck => _firstTimeConnectionCheck;

  int _revenueFilterTypeIndex = 0;
  int get revenueFilterTypeIndex => _revenueFilterTypeIndex;

  String _revenueFilterType = '';
  String get revenueFilterType => _revenueFilterType;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<dynamic> _timeZoneList = [];
  List<dynamic> get timeZoneList => _timeZoneList;
  List<String> _timeZone = [];
  List<String> get timeZone => _timeZone;

  String _selectedTimeZone = '';
  String get selectedTimeZone => _selectedTimeZone;

  void setFirstTimeConnectionCheck(bool isChecked) {
    _firstTimeConnectionCheck = isChecked;
  }

  void setRevenueFilterType(int index, bool notify) {
    _revenueFilterTypeIndex = index;
    if (notify) {
      update();
    }
  }

  void setValueForSelectedTimeZone(String setValue) {
    _selectedTimeZone = setValue;
  }
}

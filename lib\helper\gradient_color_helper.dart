import 'package:flutter/material.dart';
import 'package:tattooerp20260622/utils/color_resources.dart';

class GradientColorHelper {
  static LinearGradient gradientColor({required double opacity}) {
    return LinearGradient(
      begin: Alignment.topRight,
      end: Alignment.topLeft,
      colors: <Color>[
        // ignore: unnecessary_null_comparison
        opacity == null
            ? ColorResources.getSplashColor1()
            // ignore: deprecated_member_use
            : ColorResources.getSplashColor1().withOpacity(opacity),
        // ignore: unnecessary_null_comparison
        opacity == null
            ? ColorResources.getSplashColor2()
            : ColorResources.getSplashColor2().withOpacity(opacity),
      ],
    );
  }
}

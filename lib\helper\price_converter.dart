import 'package:flutter/material.dart';

class PriceConverter {
  static String convertPrice(BuildContext context, double price,
      {required double discount, required String discountType}) {
    if (discountType == 'amount') {
      price = price - discount;
    } else if (discountType == 'percent') {
      price = price - ((discount / 100) * price);
    }
    return '\$'
        '${(price).toStringAsFixed(2).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  static String convertWithDiscount(BuildContext context, double price,
      double discount, String discountType) {
    if (discountType == 'amount') {
      price = price - discount;
    } else if (discountType == 'percent') {
      price = price - ((discount / 100) * price);
    }
    return '\$ ' '$price';
  }

  static String discountCalculation(BuildContext context, double price,
      double discount, String discountType) {
    if (discountType == 'amount') {
      discount = discount;
    } else if (discountType == 'percent') {
      discount = ((discount / 100) * price);
    }
    return '\$ ${discount.toStringAsFixed(2)}';
  }

  static String discountCalculationWithOutSymbol(BuildContext context,
      double price, double discount, String discountType) {
    if (discountType == 'amount') {
      discount = discount;
    } else if (discountType == 'percent') {
      discount = ((discount / 100) * price);
    }
    return '${discount.toStringAsFixed(2)}';
  }

  static String calculation(
      double amount, double discount, String type, int quantity) {
    double calculatedAmount = 0;
    if (type == 'amount') {
      calculatedAmount = discount * quantity;
    } else if (type == 'percent') {
      calculatedAmount = (discount / 100) * (amount * quantity);
    }
    return '\$  ${calculatedAmount.toStringAsFixed(2)}';
  }

  static String percentageCalculation(BuildContext context, String price,
      String discount, String discountType) {
    return '$discount${discountType == 'percent' ? '%' : '\$'} OFF';
  }

  static String priceWithSymbol(double amount) {
    return '\$ ${amount.toStringAsFixed(2)}';
  }
}

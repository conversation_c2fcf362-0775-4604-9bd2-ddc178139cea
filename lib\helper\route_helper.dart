import 'package:get/get.dart';
import 'package:tattooerp20260622/screen/Home.dart';
import 'package:tattooerp20260622/screen/splash_screen.dart';

class RouteHelper {
  static const String initial = '/';
  static const String splash = '/splash';
  static const String home = '/home';
  static const String shopSettings = '/shop-settings';

  static getInitialRoute() => '$initial';
  static getSplashRoute() => '$splash';
  static getHomeRoute(String name) => '$home?name=$name';
  static getShopSettings() => '$shopSettings';

  static List<GetPage> routes = [
    GetPage(name: initial, page: () => SplashScreen()),
    GetPage(name: splash, page: () => SplashScreen()),
    GetPage(name: home, page: () => Home()),
  ];
}

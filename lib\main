import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:sm_scan/shangmi_scan_mixin.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const MyHomePage(title: 'Demo'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with ShangmiScanMixin<MyHomePage> {
  String code = "Waiting Scan....";
  final HttpClient _httpClient = HttpClient();
  Map<String, dynamic> goodsMap = {};
  Goods? goods;
  String wrongMessage = "";
  @override
  void initState() {
    super.initState();
    shangmiCodeHandle("920612095");
  }

  @override
  Future<void> shangmiCodeHandle(String _code) async {
    print('扫描到数据：$_code');
    code = _code;
    wrongMessage = "";
    _httpClient.getUrl(Uri.parse("http://shangmi.ustmi.com/home/<USER>" + code)).then((HttpClientRequest request) {
      return request.close();
    }).then((HttpClientResponse response) {
      // 处理response响应
      if (response.statusCode == 200) {
        response.transform(utf8.decoder).join().then((String string) {
          print(string);
          Map<String, dynamic> result = json.decode(string);
          if (result.isEmpty) {
            return;
          }
          if (result["error"] == 1) {
            goodsMap = result["content"];
            goods = Goods.fromJson(goodsMap);
            print(goodsMap);
          }
          if (result["error"] == 2 || result["error"] == 3) {
            wrongMessage = result["message"];
          }
        });
      } else {
        print("error");
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const Text('Scaned Code', style: TextStyle(height: 3)),
            Text(
              code,
              style: Theme.of(context).textTheme.headline4,
            ),
            const SizedBox(
              height: 10,
            ),
            wrongMessage != ""
                ? Text(
                    wrongMessage,
                    style: const TextStyle(fontSize: 20, color: Colors.red, height: 5),
                  )
                : Container(),
            goods != null
                ? Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: const [
                            Text("ID:", style: TextStyle(height: 2)),
                            Text("Name:", style: TextStyle(height: 2)),
                            Text("spec:", style: TextStyle(height: 2)),
                            Text("LocationName:", style: TextStyle(height: 2)),
                          ],
                        ),
                      ),
                      const SizedBox(
                        width: 15,
                      ),
                      Expanded(
                          flex: 2,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(goods!.id.toString(), style: const TextStyle(height: 2)),
                              Text(goods!.name.toString(), style: const TextStyle(height: 2)),
                              Text(goods!.spec.toString(), style: const TextStyle(height: 2)),
                              Text(goods!.locationName.toString(), style: const TextStyle(height: 2)),
                            ],
                          )),
                    ],
                  )
                : Container()
          ],
        ),
      ),
    );
  }
}

class Goods {
  String? id;
  String? name;
  String? spec;
  String? locationName;
  String? barCode;
  String? pinYin;

  Goods({this.id, this.name, this.spec, this.locationName, this.barCode, this.pinYin});

  Goods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    spec = json['spec'];
    locationName = json['locationName'];
    barCode = json['barCode'];
    pinYin = json['pinYin'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = id;
    data['name'] = name;
    data['spec'] = spec;
    data['locationName'] = locationName;
    data['barCode'] = barCode;
    data['pinYin'] = pinYin;
    return data;
  }
}

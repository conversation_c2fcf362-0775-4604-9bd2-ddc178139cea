import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tattooerp20260622/providers/admin_provider.dart';
import 'package:tattooerp20260622/providers/config_provicer.dart';
import 'package:tattooerp20260622/providers/customer_provider.dart';
import 'package:tattooerp20260622/providers/instock_provider.dart';
import 'package:tattooerp20260622/providers/inventory_provider.dart';
import 'package:tattooerp20260622/providers/outstock_provider.dart';
import 'package:tattooerp20260622/providers/saleorder_provider.dart';
import 'package:tattooerp20260622/providers/stock_provider.dart';
import 'package:tattooerp20260622/providers/stripepay_provider.dart';
import 'package:tattooerp20260622/providers/transfer_provider.dart';
import 'package:tattooerp20260622/router/sale/createSale.dart';
import 'package:tattooerp20260622/screen/Home.dart';
import 'package:tattooerp20260622/screen/SellerBottomNav.dart';
import 'package:tattooerp20260622/screen/splash_screen.dart';

import 'common/global.dart';
import 'common/theme.dart' as T;
import 'model/User.dart';
import 'screen/KeeperBottomNav.dart';
import 'screen/Login.dart';
import 'screen/setting.dart';

///flutter应用程序中的入口函数
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SharedPreferences prefs = await SharedPreferences.getInstance();
  authToken = prefs.getString("user_token");
  runApp(App(authToken));
}

///应用的根布局
class App extends StatelessWidget {
  final String? token;
  static Admin adminInfo = Admin();
  static Map<String, String> sysConfig = Map();
  static Reader? reader;
  const App(this.token, {Key? key}) : super(key: key);
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    ScreenUtil.init(context, designSize: const Size(750, 1334), minTextAdapt: true);
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => T.Theme()),
        ChangeNotifierProvider(create: (_) => SaleOrderProvider()),
        ChangeNotifierProvider(create: (_) => AdminProvider()),
        ChangeNotifierProvider(create: (_) => StockProvider()),
        ChangeNotifierProvider(create: (_) => TransferProvider()),
        ChangeNotifierProvider(create: (_) => InstockProvider()),
        ChangeNotifierProvider(create: (_) => OutStockProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => ConfigProvider()),
        ChangeNotifierProvider(create: (_) => InventoryProvider()),
        ChangeNotifierProvider(create: (_) => StripePayProvider()),
      ],
      child: MaterialApp(
        navigatorKey: App.navigatorKey,
        debugShowCheckedModeBanner: false,
        title: "SunmiScanErp",
        initialRoute: "/splashScreen",
        home: token == null ? Login() : Home(),
        theme: ThemeData(
          brightness: Brightness.light,
          primaryColor: Colors.lightBlue[800],
          dividerColor: Colors.grey[300],
          //fontFamily: 'SourceHanSans',
          textTheme: const TextTheme(
            titleSmall: TextStyle(fontSize: 72.0, fontWeight: FontWeight.bold),
            labelSmall: TextStyle(fontSize: 20.0),
            titleMedium: TextStyle(fontSize: 14.0, fontFamily: 'Hind'),
          ),
        ),
        //注册路由表
        routes: {
          "home": (context) => Home(),
          "login": (context) => Login(),
          "splashScreen": (context) => SplashScreen(),
          "keeperNav": (context) => const KeeperBottomMav(),
          "sellerNav": (context) => const SellerBottomMav(),
          "createSale": (context) => const CreateSale(),
          "setting": (context) => Setting(),
        },
        builder: EasyLoading.init(),
        onGenerateRoute: (settings) {
          return MaterialPageRoute(builder: (context) {
            return SplashScreen();
          });
        },
      ),
    );
  }
}

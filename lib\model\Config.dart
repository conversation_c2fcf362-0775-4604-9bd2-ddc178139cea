/*
 * @Author: your name
 * @Date: 2020-12-07 08:20:56
 * @LastEditTime: 2020-12-09 15:33:33
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \microSocial\lib\model\User.dart
 */
class Config {
  Config({
    this.id,
    this.name,
    this.value,
    this.tips,
    this.parent_id,
    this.desc = 0,
  });
  dynamic id = "";
  dynamic name = "";
  dynamic value = "";
  dynamic tips = "";
  dynamic parent_id = "";
  dynamic desc = "";
  dynamic visiable = "";

  Config.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        name = json["name"],
        value = json["value"],
        tips = json["tips"],
        parent_id = json["parent_id"],
        desc = json["desc"],
        visiable = json["visiable"];
}

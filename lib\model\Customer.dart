class Customer {
  dynamic id = "";
  dynamic customerName = "";
  dynamic reference = "";
  dynamic consignee = "";
  dynamic address = "";
  dynamic country = "";
  dynamic state = "";
  dynamic city = "";
  dynamic zip = "";
  dynamic add_user_id = "";
  dynamic add_user_name = "";
  dynamic remark = "";
  dynamic updateTime = "";
  dynamic createTime = "";
  dynamic tel = "";
  dynamic user_rank = "";
  dynamic rank_name = "";
  dynamic email = "";
  dynamic discount_number = 0;
  dynamic wholesaler_type_name = "";
  dynamic wholesaler_type = "";

  Customer({
    dynamic id,
    dynamic customerName,
    dynamic reference,
    dynamic consignee,
    dynamic address,
    dynamic country,
    dynamic state,
    dynamic city,
    dynamic zip,
    dynamic add_user_id,
    dynamic add_user_name,
    dynamic remark,
    dynamic updateTime,
    dynamic createTime,
    dynamic tel,
    dynamic user_rank,
    dynamic rank_name,
    dynamic email,
    dynamic discount_number,
    dynamic wholesaler_type,
    dynamic wholesaler_type_name,
  });

  Customer.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        customerName = json['customerName'],
        reference = json['reference'],
        consignee = json['consignee'],
        address = json['address'],
        country = json['country'],
        state = json['state'],
        city = json['city'],
        zip = json['zip'],
        add_user_id = json['add_user_id'],
        add_user_name = json['add_user_name'],
        remark = json['remark'],
        updateTime = json['updateTime'],
        createTime = json['createTime'],
        tel = json['tel'],
        user_rank = json['user_rank'],
        rank_name = json['rank_name'],
        discount_number = json['discount_number'],
        wholesaler_type = json['wholesaler_type'],
        wholesaler_type_name = json['wholesaler_type_name'],
        email = json['email'];

  Map<String, dynamic> toJson() => {
        'customerName': customerName.toString(),
        'remark': remark.toString(),
        'tel': tel.toString(),
        'email': email.toString(),
        'reference': "offline",
        'user_rank': "0",
      };
}

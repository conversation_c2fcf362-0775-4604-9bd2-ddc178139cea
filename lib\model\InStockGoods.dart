import 'package:flutter/widgets.dart';

import 'StockGoods.dart';

class InStockGoods {
  dynamic id = "";
  dynamic goods_name = "";
  dynamic goods_id = "";
  dynamic order_id = "";
  dynamic sku = "";
  dynamic num = "";
  dynamic code = "";
  List<CategoryAttr> category_attr = const [];
  dynamic in_num = "";
  TextEditingController controllerr = TextEditingController();

  InStockGoods({
    dynamic id,
    dynamic goods_name,
    dynamic goods_id,
    dynamic order_id,
    dynamic sku,
    dynamic num,
    dynamic code,
    List<CategoryAttr> category_attr = const [],
    dynamic in_num,
  });

  InStockGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        goods_name = json['goods_name'],
        goods_id = json['goods_id'],
        order_id = json['order_id'],
        sku = json['sku'],
        num = json['num'],
        code = json['code'],
        in_num = json['in_num'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
        'goods_name': goods_name.toString(),
        'goods_id': goods_id.toString(),
        'order_id': order_id.toString(),
        'sku': sku.toString(),
        'num': num.toString(),
        'code': code.toString(),
        'in_num': in_num.toString()
      };
}

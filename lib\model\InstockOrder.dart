class InstockOrder {
  dynamic id = "";
  dynamic createTime = "";
  dynamic order_no = "Create Sale Order";
  dynamic supplier_id = "";
  dynamic supplier_name = "";
  dynamic order_amount = "";
  dynamic order_status = "";
  dynamic add_user_id = "";
  dynamic add_user_name = "";
  dynamic store_id = "";
  dynamic store_name = "";
  dynamic updateTime = "";
  dynamic status_name = "";

  InstockOrder({
    id,
    createTime,
    order_no = "Create Sale Order",
    supplier_id,
    supplier_name,
    order_amount,
    order_status,
    add_user_id,
    add_user_name,
    store_id,
    store_name,
    updateTime,
    status_name,
  });

  InstockOrder.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        createTime = json['createTime'],
        updateTime = json['updateTime'],
        order_no = json['order_no'],
        order_status = json['order_status'],
        add_user_id = json['add_user_id'],
        add_user_name = json['add_user_name'],
        store_id = json['store_id'],
        store_name = json['store_name'],
        supplier_id = json['supplier_id'],
        supplier_name = json['supplier_name'],
        order_amount = json['order_amount'],
        status_name = json['status_name'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
      };
}

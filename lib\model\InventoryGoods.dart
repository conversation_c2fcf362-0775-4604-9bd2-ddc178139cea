class InventoryGoods {
  dynamic id = "";
  dynamic goods_name = "";
  dynamic goods_id = "";
  dynamic category_attr = "";
  dynamic sku = "";
  dynamic code = "";
  dynamic num = "";
  dynamic createTime = "";
  dynamic updateTime = "";
  dynamic deal_status = "";
  dynamic order_id = "";
  dynamic stock_num = "";

  InventoryGoods({
    id,
    goods_name,
    goods_id,
    category_attr,
    sku,
    code,
    num,
    createTime,
    updateTime,
    deal_status,
    order_id,
    stock_num,
  });

  InventoryGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'].toString(),
        createTime = json['createTime'],
        updateTime = json['updateTime'],
        goods_name = json['goods_name'],
        goods_id = json['goods_id'],
        category_attr = json['category_attr'],
        sku = json['sku'],
        code = json['code'],
        deal_status = json['deal_status'],
        order_id = json['order_id'],
        stock_num = json['stock_num'],
        num = json['num'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
      };
}

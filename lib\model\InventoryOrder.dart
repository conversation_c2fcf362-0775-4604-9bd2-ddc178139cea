class InventoryOrder {
  String id = "";
  dynamic createTime = "";
  dynamic order_no = "";
  dynamic order_status = "";
  dynamic add_user_id = "";
  dynamic add_user_name = "";
  dynamic store_id = "";
  dynamic store_name = "";
  dynamic status_name = "";

  InventoryOrder({
    id,
    createTime,
    order_no,
    order_status,
    add_user_id,
    add_user_name,
    store_id,
    store_name,
    status_name,
  });

  InventoryOrder.fromJson(Map<String, dynamic> json)
      : id = json['id'].toString(),
        createTime = json['createTime'],
        order_no = json['order_no'],
        order_status = json['order_status'],
        add_user_id = json['add_user_id'],
        add_user_name = json['add_user_name'],
        store_id = json['store_id'],
        store_name = json['store_name'],
        status_name = json['status_name'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
      };
}

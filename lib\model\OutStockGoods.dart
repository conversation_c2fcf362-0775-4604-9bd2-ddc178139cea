import 'package:flutter/cupertino.dart';

import 'StockGoods.dart';

class OutStockGoods {
  dynamic id = "";
  dynamic goods_name = "";
  dynamic goods_id = "";
  dynamic order_id = "";
  dynamic sku = "";
  dynamic num = "";
  dynamic code = "";
  List<CategoryAttr> category_attr = const [];
  dynamic out_num = "";
  TextEditingController controllerr = TextEditingController();

  OutStockGoods({
    dynamic id,
    dynamic goods_name,
    dynamic goods_id,
    dynamic order_id,
    dynamic sku,
    dynamic num,
    dynamic code,
    List<CategoryAttr> category_attr = const [],
    dynamic out_num,
  });

  OutStockGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        goods_name = json['goods_name'],
        goods_id = json['goods_id'],
        order_id = json['order_id'],
        sku = json['sku'],
        num = json['num'],
        code = json['code'],
        out_num = json['out_num'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
        'goods_name': goods_name.toString(),
        'goods_id': goods_id.toString(),
        'order_id': order_id.toString(),
        'sku': sku.toString(),
        'num': num.toString(),
        'code': code.toString(),
        'out_num': out_num.toString()
      };
}

class OutStockOrder {
  String id = "";
  dynamic createTime = "";
  dynamic order_no = "";
  dynamic customer_id = "";
  dynamic customer_name = "";
  dynamic order_amount = "";
  dynamic order_status = "";
  dynamic add_user_id = "";
  dynamic add_user_name = "";
  dynamic store_id = "";
  dynamic store_name = "";
  dynamic updateTime = "";
  dynamic status_name = "";

  OutStockOrder({
    id,
    createTime,
    order_no,
    customer_id,
    customer_name,
    order_amount,
    order_status,
    add_user_id,
    add_user_name,
    store_id,
    store_name,
    updateTime,
    status_name,
  });

  OutStockOrder.fromJson(Map<String, dynamic> json)
      : id = json['id'].toString(),
        createTime = json['createTime'],
        updateTime = json['updateTime'],
        order_no = json['order_no'],
        order_status = json['order_status'],
        add_user_id = json['add_user_id'],
        add_user_name = json['add_user_name'],
        store_id = json['store_id'],
        store_name = json['store_name'],
        customer_id = json['customer_id'],
        customer_name = json['customer_name'],
        status_name = json['status_name'],
        order_amount = json['order_amount'];

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
      };
}

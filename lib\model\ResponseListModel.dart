class ResponseListModel {
  ResponseListModel({
    endRow,
    hasNextPage,
    hasPreviousPage,
    isFirstPage,
    isLastPage,
    navigateFirstPage,
    navigateLastPage,
    navigatePages,
    navigatepageNums,
    nextPage,
    pageNum,
    pageSize,
    pages,
    prePage,
    size,
    startRow,
    total,
    list,
  });

  int endRow = 0;
  bool hasNextPage = false;
  bool hasPreviousPage = false;
  bool isFirstPage = false;
  bool isLastPage = false;
  int navigateFirstPage = 0;
  int navigateLastPage = 0;
  int navigatePages = 0;
  List<dynamic> navigatepageNums = [];
  int nextPage = 0;
  dynamic pageNum = 0;
  dynamic pageSize = 0;
  dynamic pages = 0;
  dynamic prePage = 0;
  dynamic size = 0;
  dynamic startRow = 0;
  dynamic total = "0";
  List<dynamic> list = [];

  ResponseListModel.fromJson(Map<String, dynamic> json)
      : endRow = json["endRow"] ?? 0,
        hasNextPage = json["hasNextPage"] ?? 0,
        hasPreviousPage = json["hasPreviousPage"] ?? 0,
        isFirstPage = json["isFirstPage"] ?? 0,
        isLastPage = json["isLastPage"] ?? 0,
        navigateFirstPage = json["navigateFirstPage"] ?? 0,
        navigateLastPage = json["navigateLastPage"] ?? 0,
        navigatePages = json["navigatePages"] ?? 0,
        navigatepageNums = json["navigatepageNums"] ?? 0,
        nextPage = json["nextPage"] ?? 0,
        pageNum = json["pageNum"] ?? 1,
        pageSize = json["pageSize"] ?? 0,
        pages = json["pages"] ?? 0,
        prePage = json["prePage"] ?? 0,
        size = json["size"] ?? 0,
        startRow = json["startRow"] ?? 0,
        total = json["total"] ?? "",
        list = json["list"] ?? "";
}

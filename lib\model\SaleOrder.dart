import 'package:tattooerp20260622/main.dart';

class Saleorder {
  dynamic id = 0;
  String sale_order_type = "";
  String add_time = "";
  String order_no = "";
  dynamic customer_id = 0;
  String customer_name = "";
  dynamic order_amount = 0.00;
  dynamic shipping_fee = 0.00;
  String pay_method = "";
  String consignee = "";
  String address = "";
  String post_code = "";
  String tel = "";
  dynamic store_id = App.adminInfo.store_id;
  dynamic cost_price_amount = 0.00;
  dynamic goods_amount = 0.00;
  dynamic add_user_id = 0;
  String add_user_name = "";
  dynamic profit_amount = 0.00;
  String store_name = App.adminInfo.store_name;
  dynamic discount_amount = 0.00;
  String createTime = "";
  String updateTime = "";
  String status_name = "";
  dynamic user_discount_amount = 0.0;
  int user_discount_number = 100;
  dynamic tax_amount = 0.00;
  dynamic shop_discount_amount = 0.00;
  String reference = "";
  dynamic cash = 0.00;
  dynamic pos = 0.00;
  String shipping_code = "";
  dynamic shippment_id = "";
  dynamic order_status = 0;

  Saleorder({
    id,
    sale_order_type,
    add_time,
    order_no,
    customer_id,
    customer_name,
    order_amount = 0.0,
    shipping_fee = 0.0,
    fax_fee = 0.0,
    pay_method,
    consignee,
    address,
    post_code,
    tel,
    order_status,
    cost_price_amount = 0.0,
    goods_amount = 0.0,
    add_user_id,
    add_user_name,
    profit_amount = 0.0,
    store_id,
    store_name,
    discount_amount = 0.0,
    createTime,
    updateTime,
    status_name,
    user_discount_amount = 0.0,
    user_discount_number = 0.0,
    tax_amount = 0.0,
    shop_discount_amount = 0.0,
    reference,
    cash = 0.0,
    pos = 0.0,
    shipping_code,
    shippment_id,
  });

  Saleorder.fromJson(Map<String, dynamic> json)
      : id = json['id'] ?? "",
        sale_order_type = json['sale_order_type'] ?? "",
        order_no = json['order_no'] ?? "",
        customer_id = json['customer_id'] ?? 0,
        customer_name = json['customer_name'] ?? "",
        order_amount = json['order_amount'] ?? 0.00,
        shipping_fee = json['shipping_fee'] ?? 0.00,
        pay_method = json['pay_method'] ?? "",
        consignee = json['consignee'] ?? "",
        address = json['address'] ?? "",
        post_code = json['post_code'] ?? "",
        tel = json['tel'] ?? "",
        order_status = json['order_status'] ?? 0,
        cost_price_amount = json['cost_price_amount'] ?? 0.00,
        goods_amount = json['goods_amount'] ?? 0.00,
        add_user_id = json['add_user_id'] ?? 0,
        add_user_name = json['add_user_name'] ?? "",
        profit_amount = json['profit_amount'] ?? 0.00,
        store_id = json['store_id'] ?? 0,
        store_name = json['store_name'] ?? "",
        discount_amount = json['discount_amount'] ?? 0.00,
        createTime = json['createTime'] ?? "",
        updateTime = json['updateTime'] ?? "",
        status_name = json['status_name'] ?? "",
        user_discount_amount = json['user_discount_amount'] ?? 0.00,
        user_discount_number = json['user_discount_number'] ?? 100,
        tax_amount = json['tax_amount'] ?? 0.00,
        shop_discount_amount = json['shop_discount_amount'] ?? 0.00,
        reference = json['reference'] ?? "",
        cash = json['cash'] ?? 0.00,
        pos = json['pos'] ?? 0.00,
        shipping_code = json['shipping_code'] ?? "",
        shippment_id = json['shippment_id'] ?? 0;

  Map<String, dynamic> toJson() => {
        'id': id.toString(),
        'customer_id': customer_id.toString(),
        'customer_name': customer_name.toString(),
        'order_amount': order_amount.toString(),
        'pay_method': pay_method.toString(),
        'tel': tel.toString(),
        'goods_amount': goods_amount.toString(),
        'store_id': store_id,
        'discount_amount': discount_amount.toString(),
        'user_discount_amount': user_discount_amount.toString(),
        'user_discount_number': user_discount_number.toString(),
        'tax_amount': tax_amount.toString(),
        'shop_discount_amount': shop_discount_amount.toString(),
        'reference': "offline",
        'cash': cash.toString(),
        'pos': pos.toString(),
      };
}

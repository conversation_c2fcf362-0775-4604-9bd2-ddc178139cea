import 'package:flutter/widgets.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';

class SaleOrderGoods {
  dynamic id = "";
  dynamic goods_name = "";
  dynamic goods_id = "";
  dynamic sale_price = "";
  dynamic order_id = "";
  dynamic category_str = "";
  dynamic sku = "";
  int num = 1;
  List<CategoryAttr> category_attr = [];
  dynamic code = "";
  dynamic cate_id = "";
  dynamic origin_sale_price = "";
  dynamic wholesaler_price1 = "";
  dynamic wholesaler_price2 = "";

  TextEditingController controllerr = TextEditingController();

  SaleOrderGoods({
    id,
    goods_name,
    goods_id,
    sale_price,
    order_id,
    category_str,
    sku,
    num,
    category_attr,
    code,
    cate_id,
    controller,
    origin_sale_price,
    wholesaler_price1,
    wholesaler_price2,
  });

  SaleOrderGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        goods_name = json['goods_name'],
        goods_id = json['goods_id'],
        sale_price = json['sale_price'],
        order_id = json['order_id'] ?? 0,
        sku = json['sku'],
        code = json['code'] ?? "",
        cate_id = json['cate_id'] ?? "0",
        origin_sale_price = json['origin_sale_price'] ?? "0",
        wholesaler_price1 = json['wholesaler_price1'] ?? "0",
        wholesaler_price2 = json['wholesaler_price2'] ?? "0",
        num = json['num'] ?? 0;

  Map<String, dynamic> toJson() {
    var tempAttr = category_attr.map((e) => e.toJson()).toList();
    return {
      'id': id,
      'goods_name': goods_name.toString(),
      'goods_id': goods_id,
      'sale_price': sale_price,
      'order_id': order_id.toString(),
      'sku': sku.toString(),
      'num': num,
      'code': code.toString(),
      'category_attr': tempAttr,
      'origin_sale_price': origin_sale_price,
      'wholesaler_price1': wholesaler_price1,
      'wholesaler_price2': wholesaler_price2,
    };
  }
}

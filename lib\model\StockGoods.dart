/*
 * @Author: your name
 * @Date: 2020-12-07 08:20:56
 * @LastEditTime: 2020-12-09 15:33:33
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \microSocial\lib\model\User.dart
 */

import 'dart:convert';

class CategoryAttr {
  CategoryAttr({this.name, this.value});
  dynamic name = "";
  dynamic value = "";
  CategoryAttr.fromJson(Map<String, dynamic> json)
      : name = json['name'],
        value = json['value'];
  Map<String, dynamic> toJson() => {
        'name': name.toString(),
        'value': value.toString(),
      };
}

class StockGoods {
  StockGoods({
    this.id,
    this.goods_name,
    this.code,
    this.goods_id,
    this.stock_num,
    this.sku,
    this.category_attr = const [],
  });

  dynamic id = "";
  dynamic goods_name = "";
  // {name:String,value:String}[] category_attr;
  List<CategoryAttr> category_attr = [];
  // dynamic category_attr = [];
  dynamic code;
  dynamic goods_id;
  dynamic stock_num;
  dynamic sku;

  static List<CategoryAttr> _parseCategoryAttr(dynamic categoryAttrData) {
    try {
      // 如果数据为 null 或空，返回空列表
      if (categoryAttrData == null) {
        return [];
      }

      // 如果已经是 List 类型，尝试转换每个元素
      if (categoryAttrData is List) {
        return categoryAttrData.map((item) {
          // 如果元素已经是 Map<String, dynamic>，直接转换
          if (item is Map<String, dynamic>) {
            return CategoryAttr.fromJson(item);
          }
          // 如果是字符串，尝试解析为 JSON
          else if (item is String) {
            try {
              final parsed = jsonDecode(item);
              if (parsed is Map<String, dynamic>) {
                return CategoryAttr.fromJson(parsed);
              }
            } catch (_) {
              // 解析失败，返回默认值
              return CategoryAttr();
            }
          }
          // 其他情况返回默认值
          return CategoryAttr();
        }).toList();
      }

      // 如果是字符串，尝试解析为 JSON 数组
      if (categoryAttrData is String) {
        try {
          final parsed = jsonDecode(categoryAttrData);
          if (parsed is List) {
            return parsed.map((item) {
              return item is Map<String, dynamic>
                  ? CategoryAttr.fromJson(item)
                  : CategoryAttr();
            }).toList();
          }
        } catch (_) {
          // 解析失败，返回空列表
        }
      }

      // 如果是 Map，尝试转换为单个元素的列表
      if (categoryAttrData is Map<String, dynamic>) {
        return [CategoryAttr.fromJson(categoryAttrData)];
      }

      // 其他无法处理的情况返回空列表
      return [];
    } catch (e) {
      // 任何异常情况下返回空列表
      return [];
    }
  }

  StockGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        goods_name = json['goods_name'],
        code = json['code'],
        sku = json['sku'],
        goods_id = json['goods_id'],
        category_attr = _parseCategoryAttr(json['category_attr']),
        stock_num = json['stock_num'];
}

class TransferOrder {
  dynamic id = "";
  dynamic createTime = "";
  dynamic order_no = "";
  dynamic order_status = "";
  dynamic add_user_id = "";
  dynamic add_user_name = "";
  dynamic from_store_id = "";
  dynamic from_store_name = "";
  dynamic updateTime = "";
  dynamic status_name = "";
  dynamic to_store_id = "";
  dynamic to_store_name = "";

  TransferOrder({
    id,
    createTime,
    order_no,
    order_status,
    add_user_id,
    add_user_name,
    from_store_id,
    from_store_name,
    updateTime,
    status_name,
    to_store_id,
    to_store_name,
  });

  TransferOrder.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        createTime = json['createTime'],
        order_no = json['order_no'],
        order_status = json['order_status'],
        add_user_id = json['add_user_id'],
        add_user_name = json['add_user_name'],
        from_store_id = json['from_store_id'],
        from_store_name = json['from_store_name'],
        updateTime = json['updateTime'],
        status_name = json['status_name'],
        to_store_id = json['to_store_id'],
        to_store_name = json['to_store_name'];
}

import 'StockGoods.dart';

class TransferOrderGoods {
  dynamic id = "";
  dynamic goods_name = "";
  dynamic goods_id = "";
  dynamic order_id = "";
  dynamic sku = "";
  dynamic num = "";
  dynamic code = "";
  List<CategoryAttr> category_attr = const [];
  dynamic in_num = "";

  TransferOrderGoods({
    dynamic id,
    dynamic goods_name,
    dynamic goods_id,
    dynamic order_id,
    dynamic sku,
    dynamic num,
    dynamic code,
    List<CategoryAttr> category_attr = const [],
    dynamic in_num,
  });

  TransferOrderGoods.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        goods_name = json['goods_name'],
        goods_id = json['goods_id'],
        order_id = json['order_id'],
        sku = json['sku'],
        num = json['num'],
        code = json['code'],
        in_num = json['in_num'];
}

/*
 * @Author: your name
 * @Date: 2020-12-07 08:20:56
 * @LastEditTime: 2020-12-09 15:33:33
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \microSocial\lib\model\User.dart
 */
class Admin {
  Admin({
    this.id,
    this.userName,
    this.roleId = 0,
    this.store_id = 0,
    this.store_name,
    this.departmentId = 0,
    this.departmentName,
    this.email,
    this.mobile,
    this.roleName,
    this.telephone,
  });

  dynamic id = "";
  dynamic userName = "";
  int roleId = 0;
  int store_id = 0;
  dynamic store_name = "";
  int departmentId = 0;
  dynamic departmentName = "";
  dynamic email = "";
  dynamic mobile = "";
  dynamic roleName = "";
  dynamic telephone = "";

  Admin.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        userName = json['userName'],
        roleId = int.parse((json['roleId'] ?? 0).toString()),
        store_id = int.parse((json['store_id'] ?? 0).toString()),
        store_name = json['store_name'],
        departmentId = int.parse((json['departmentId'] ?? 0).toString()),
        departmentName = json['departmentName'],
        email = json['email'],
        mobile = json['mobile'],
        roleName = json['roleName'],
        telephone = json['telephone'];
}

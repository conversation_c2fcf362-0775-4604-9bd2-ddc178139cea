part 'Profile.g.dart';

class Profile {
  Profile({
    baby,
    birthday,
    effect,
    fame,
    icon,
    id,
    nickname,
    nodeNum,
    password,
    phone,
    sex,
    userLevel,
    token,
  });

  num? baby;
  String? birthday;
  num? effect;
  num? fame;
  String? icon;
  num? id;
  String? nickname;
  num? nodeNum;
  String? password;
  String? phone;
  num? sex;
  String? userLevel;
  String? token;

  factory Profile.fromJson(Map<String, dynamic> json) => _$ProfileFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileToJson(this);
}

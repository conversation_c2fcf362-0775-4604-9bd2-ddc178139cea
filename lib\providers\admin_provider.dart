import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tattooerp20260622/model/User.dart';

import '../utils/storage.dart';

class AdminProvider with ChangeNotifier {
  Admin adminInfo = Admin(
    id: "",
    userName: "",
    roleId: 0,
    store_id: 0,
    store_name: "",
    departmentId: 0,
    departmentName: "",
    email: "",
    mobile: "",
    roleName: "",
    telephone: "",
  );

  bool isSeller = false;
  getAdminInfo() async {
    await LocalStorage.getString("user_info").then((e) {
      adminInfo = Admin.fromJson(jsonDecode(e));
      print(adminInfo.store_id);
      if (adminInfo.store_id > 1) {
        isSeller = true;
      }
    });
    notifyListeners();
  }
}

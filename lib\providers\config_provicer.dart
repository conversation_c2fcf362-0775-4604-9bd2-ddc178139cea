import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tattooerp20260622/main.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

class ConfigProvider with ChangeNotifier {
  getConfig() async {
    return await HttpManager.getInstance()
        .get("/default/get-config?id=2", cache: 0)
        .then((e) {
      App.sysConfig["taxRate"] = e["data"]["value"];
      return true;
    });
  }
}

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/model/Customer.dart';
import 'package:tattooerp20260622/providers/saleorder_provider.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../model/ResponseListModel.dart';

class CustomerProvider with ChangeNotifier {
  List<Customer> customerList = [];
  int page = 1;
  String searchTel = "";
  Future<bool> loadList() async {
    page = 1;
    customerList = [];
    return fetchList();
  }

  Customer ccreateCustomer = Customer();
  Future<bool> fetchList() async {
    return await HttpManager.getInstance()
        .get(
      "/customer/list?pageNum=$page&filters[tel]=$searchTel",
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        customerList.add(Customer.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  void setCustomer({
    customerName = "",
    tel = "",
    email = "",
    remark = "",
  }) {
    ccreateCustomer.customerName = customerName;
    ccreateCustomer.tel = tel;
    ccreateCustomer.email = email;
    ccreateCustomer.remark = remark;
  }

  Future<bool> createCustomer(BuildContext _context) async {
    return await HttpManager.getInstance()
        .post(
      "/customer/insert",
      cache: 0,
      params: ccreateCustomer.toJson(),
    )
        .then((e) {
      if (e["code"] == 0) {
        ccreateCustomer.id = e["content"];
        _context.read<SaleOrderProvider>().createCustomer = ccreateCustomer;
        _context.read<SaleOrderProvider>().createSaleOrder.customer_id =
            e["data"].toString();

        notifyListeners();
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        return true;
      } else {
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        return false;
      }
    });
  }
}

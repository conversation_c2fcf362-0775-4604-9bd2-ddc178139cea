import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tattooerp20260622/model/InStockGoods.dart';
import 'package:tattooerp20260622/model/InstockOrder.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../model/ResponseListModel.dart';

class InstockProvider with ChangeNotifier {
  InstockOrder instockOrder = InstockOrder();
  List<InStockGoods> orderGoodsList = [];
  List<InstockOrder> orderList = [];
  int page = 1;
  String searchOrderNo = "";
  Future<bool> loadList() async {
    page = 1;
    orderList = [];
    return fetchList();
  }

  Future<bool> fetchList() async {
    return await HttpManager.getInstance()
        .get(
      "/instock/list?pageNum=" +
          page.toString() +
          "&filters[order_no]=" +
          searchOrderNo,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        orderList.add(InstockOrder.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  getDetail(id) async {
    return await HttpManager.getInstance()
        .get(
      "/instock/view?id=" + id,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      orderGoodsList = [];
      instockOrder = InstockOrder();
      instockOrder = InstockOrder.fromJson(e["data"]["instockOrder"]);
      // TransferOrderGoods transferOrderGoods = TransferOrderGoods.fromJson(e["data"]["transferOrderGoods"]);
      for (var item in e["data"]["instockOrderGoods"]) {
        var tempItem = InStockGoods.fromJson(item);
        List<CategoryAttr> tempCateAttr = [];
        if (item["category_attr"] != null && item["category_attr"].length > 0) {
          for (var prop in item["category_attr"]) {
            tempCateAttr.add(CategoryAttr.fromJson(prop));
          }

          tempItem.category_attr = tempCateAttr;
        } else {
          tempItem.category_attr = tempCateAttr;
        }

        TextEditingController tempController = TextEditingController();

        tempItem.controllerr = tempController;
        tempItem.controllerr.text = tempItem.in_num.toString();
        orderGoodsList.add(tempItem);
      }

      notifyListeners();
    });
  }

  void plusGoodsNum(_index) {
    if (_index <= orderGoodsList.length) {
      orderGoodsList[_index].in_num++;
      notifyListeners();
    }
  }

  minusGoodsNum(_index) {
    if (_index <= orderGoodsList.length) {
      orderGoodsList[_index].in_num--;
      notifyListeners();
    }
  }

  saveOrder() async {
    if (instockOrder.id <= 0) {
      return;
    }
    List<Map<String, dynamic>> goodsListJson = [];

    for (var e in orderGoodsList) {
      goodsListJson.add(e.toJson());
    }

    await HttpManager.getInstance().post(
      "/instock/update?id=" + instockOrder.id.toString(),
      cache: 0,
      params: {
        "instockOrder": instockOrder.toJson(),
        "instockOrderGoods": goodsListJson
      },
    ).then((e) {
      Fluttertoast.showToast(
        msg: "Successfully",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black45,
        textColor: Colors.white,
        fontSize: 15.0,
      );
    });
  }

  confirmOrder() async {
    if (instockOrder.id <= 0) {
      return;
    }
    await saveOrder();
    await HttpManager.getInstance()
        .post("/instock/confirm?id=" + instockOrder.id.toString(), cache: 0)
        .then(
      (e) {
        Fluttertoast.showToast(
          msg: "Successfully",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        instockOrder.order_status = 1;
        instockOrder.status_name = "Admitted";
        notifyListeners();
      },
    );
  }

  addGoodsNum(String _sku) {
    for (var e in orderGoodsList) {
      if (e.sku == _sku) {
        e.in_num++;
        e.controllerr.text = e.in_num.toString();
        break;
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../model/InventoryGoods.dart';
import '../model/InventoryOrder.dart';
import '../model/ResponseListModel.dart';

class InventoryProvider with ChangeNotifier {
  InventoryOrder inventory = InventoryOrder(); //InventoryGoods
  List<InventoryGoods> orderGoodsList = [];
  List<InventoryOrder> orderList = [];
  int page = 1;
  int skuCount = 0;
  int goodsNumber = 0;
  String searchOrderNo = "";
  Future<bool> loadList() async {
    page = 1;
    orderList = [];
    return fetchList();
  }

  Future<bool> fetchList() async {
    return await HttpManager.getInstance()
        .get(
      "/inventory/list?pageNum=$page&filters[order_no]=$searchOrderNo",
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        orderList.add(InventoryOrder.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  getDetail(id) async {
    return await HttpManager.getInstance()
        .get(
      "/inventory/view?id=$id",
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      orderGoodsList = [];
      inventory = InventoryOrder();
      inventory = InventoryOrder.fromJson(e["data"]["inventory"]);

      skuCount = int.parse(e["data"]['orderView']['skuCount'].toString());
      goodsNumber = int.parse(e["data"]['orderView']['goodsNum'].toString());

      // TransferOrderGoods transferOrderGoods = TransferOrderGoods.fromJson(e["data"]["transferOrderGoods"]);
      for (var item in e["data"]["inventoryGoods"]) {
        var tempItem = InventoryGoods.fromJson(item);
        List<CategoryAttr> tempCateAttr = [];
        if (item["category_attr"] != null && item["category_attr"].length > 0) {
          for (var prop in item["category_attr"]) {
            tempCateAttr.add(CategoryAttr.fromJson(prop));
          }

          tempItem.category_attr = tempCateAttr;
        } else {
          tempItem.category_attr = tempCateAttr;
        }

        orderGoodsList.add(tempItem);
      }

      notifyListeners();
    });
  }

  addGoodsNum(String _code) async {
    //提交添加数量
    return await HttpManager.getInstance()
        .get(
      "/inventory/search-and-insert?code=$_code&order_id=${inventory.id}",
      cache: 0,
    )
        .then((e) {
      print(e);
    });
  }

  //如果存在 就修改数量,如果不存在 就插入
  pushGoods(bool flag, InventoryGoods _goods) {
    ////根据返回时新建 还是 追加 使用不同操作
    if (flag) {
      orderGoodsList.forEach((element) {
        if (_goods.id.toString() == element.id.toString()) {
          // 获取当前元素的索引
          int index = orderGoodsList.indexOf(element);
          // 更新List中的元素
          orderGoodsList[index].num =
              int.parse(orderGoodsList[index].num.toString()) + 1;
        }
      });
    } else {
      orderGoodsList.insert(0, _goods);
    }
    goodsNumber++;

    notifyListeners();
  }

  minusGoodsNum(_index) {
    if (_index <= orderGoodsList.length) {
      orderGoodsList[_index].num--;
      notifyListeners();
    }
  }
}

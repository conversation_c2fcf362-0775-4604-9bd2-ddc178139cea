import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tattooerp20260622/model/OutStockGoods.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../model/OutStockOrder.dart';
import '../model/ResponseListModel.dart';

class OutStockProvider with ChangeNotifier {
  OutStockOrder outStockOrder = OutStockOrder();
  List<OutStockGoods> orderGoodsList = [];
  List<OutStockOrder> orderList = [];
  int page = 1;
  String searchOrderNo = "";
  Future<bool> loadList() async {
    page = 1;
    orderList = [];
    return fetchList();
  }

  Future<bool> fetchList() async {
    return await HttpManager.getInstance()
        .get(
      "/outstock/list?pageNum=" +
          page.toString() +
          "&filters[order_no]=" +
          searchOrderNo,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        orderList.add(OutStockOrder.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  getDetail(id) async {
    return await HttpManager.getInstance()
        .get(
      "/outstock/view?id=" + id,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      orderGoodsList = [];
      outStockOrder = OutStockOrder();
      outStockOrder = OutStockOrder.fromJson(e["data"]["outstockOrder"]);
      for (var item in e["data"]["outstockOrderGoods"]) {
        var tempItem = OutStockGoods.fromJson(item);
        List<CategoryAttr> tempCateAttr = [];
        if (item["category_attr"] != null && item["category_attr"].length > 0) {
          for (var prop in item["category_attr"]) {
            tempCateAttr.add(CategoryAttr.fromJson(prop));
          }
          tempItem.category_attr = tempCateAttr;
        } else {
          tempItem.category_attr = tempCateAttr;
        }

        TextEditingController tempController = TextEditingController();

        tempItem.controllerr = tempController;
        tempItem.controllerr.text = tempItem.out_num.toString();

        orderGoodsList.add(tempItem);
      }

      notifyListeners();
    });
  }

  void plusGoodsNum(_index) {
    if (_index <= orderGoodsList.length) {
      orderGoodsList[_index].out_num++;
      notifyListeners();
    }
  }

  minusGoodsNum(_index) {
    if (_index <= orderGoodsList.length) {
      orderGoodsList[_index].out_num--;
      notifyListeners();
    }
  }

  saveOrder() async {
    if (int.parse(outStockOrder.id) <= 0) {
      return;
    }
    List<Map<String, dynamic>> goodsListJson = [];

    for (var e in orderGoodsList) {
      goodsListJson.add(e.toJson());
    }

    await HttpManager.getInstance().post(
      "/outstock/update?id=" + outStockOrder.id.toString(),
      cache: 0,
      params: {
        "outstockOrder": outStockOrder.toJson(),
        "outstockOrderGoods": goodsListJson
      },
    ).then((e) {
      Fluttertoast.showToast(
        msg: "Successfully",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black45,
        textColor: Colors.white,
        fontSize: 15.0,
      );
    });
  }

  confirmOrder() async {
    if (int.parse(outStockOrder.id) <= 0) {
      return;
    }
    await saveOrder();
    await HttpManager.getInstance()
        .post("/outstock/confirm?id=" + outStockOrder.id.toString(), cache: 0)
        .then(
      (e) {
        Fluttertoast.showToast(
          msg: "Successfully",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        outStockOrder.order_status = 1;
        outStockOrder.status_name = "Admitted";
        notifyListeners();
      },
    );
  }

  addGoodsNum(String _sku) {
    for (var e in orderGoodsList) {
      if (e.sku == _sku) {
        e.out_num++;
        e.controllerr.text = e.out_num.toString();
        notifyListeners();
        break;
      }
    }
  }
}

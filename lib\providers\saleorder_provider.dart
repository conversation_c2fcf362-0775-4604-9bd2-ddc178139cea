import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tattooerp20260622/model/Customer.dart';
import 'package:tattooerp20260622/model/SaleOrderGoods.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../main.dart';
import '../model/ResponseListModel.dart';
import '../model/SaleOrder.dart';

class SaleOrderProvider with ChangeNotifier {
  Saleorder saleOrder = Saleorder();
  List<SaleOrderGoods> orderGoodsList = [];
  List<Saleorder> orderList = [];

  int page = 1;
  String searchOrderNo = "";

  Customer createCustomer = Customer();
  Saleorder createSaleOrder = Saleorder();
  List<SaleOrderGoods> createOrderGoodsList = [];

  Future<bool> loadList() async {
    page = 1;
    orderList = [];
    return fetchList();
  }

  Future<bool> fetchList() async {
    return await HttpManager.getInstance()
        .get(
      "/sell/list?pageNum=$page&filters[store_id]=${App.adminInfo.store_id}&filters[order_no]=$searchOrderNo",
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        orderList.add(Saleorder.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  TextEditingController cashController = TextEditingController();
  TextEditingController posController = TextEditingController();
  TextEditingController discountController = TextEditingController();

  searchGoods(String _sku) async {
    for (SaleOrderGoods orderGoods in createOrderGoodsList) {
      if (orderGoods.sku == _sku) {
        orderGoods.num += 1;
        orderGoods.controllerr.text = orderGoods.num.toString();
        calculatePrice();
        return;
      }
    }

    return await HttpManager.getInstance()
        .get(
      "/default/get-goods-by-sku?sku=$_sku",
      cache: 0,
    )
        .then((e) {
      if (e["code"] == 0) {
        SaleOrderGoods _goods = SaleOrderGoods.fromJson(e["data"]);
        if (e["data"]["category_attr"] != null && e["data"]["category_attr"].length > 0) {
          for (var prop in e["data"]["category_attr"]) {
            _goods.category_attr.add(CategoryAttr.fromJson(prop));
          }
        }
        _goods.num = 1;
        _goods.controllerr = TextEditingController();
        _goods.controllerr.text = _goods.num.toString();

        createOrderGoodsList.add(_goods);
        calculatePrice();
      } else {
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.red,
          fontSize: 15.0,
        );
      }
    });
  }

  Future<void> deleteGoods(int _id) async {
    await HttpManager.getInstance()
        .post(
      "/sell/delete-goods?id=$_id",
      cache: 0,
    )
        .then((e) {
      Fluttertoast.showToast(
        msg: e["msg"],
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black45,
        textColor: Colors.white,
        fontSize: 15.0,
      );
      if (e['code'] == 0) {
        for (SaleOrderGoods _orderGoods in createOrderGoodsList) {
          if (int.parse(_orderGoods.id.toString()) == _id) {
            createOrderGoodsList.remove(_orderGoods);
            calculatePrice();
            break;
          }
        }
      }
    });
  }

  setCustomer(Customer _cus) {
    createCustomer = _cus;
    calculatePrice();
    notifyListeners();
  }

  createNewOrder() {
    createCustomer = Customer();
    createSaleOrder = Saleorder();
    createOrderGoodsList = [];
    cashController = TextEditingController();
    cashController.text = "0.0";
    posController = TextEditingController();
    posController.text = "0.0";
    discountController = TextEditingController();
    discountController.text = "0.0";
    notifyListeners();
  }

  setCreateSaleOrder(int _orderId) async {
    await HttpManager.getInstance().get("/sell/view?id=" + _orderId.toString(), cache: 0).then((e) {
      createSaleOrder = Saleorder.fromJson(e['data']['sellOrder']);

      createOrderGoodsList = [];
      for (var item in e['data']['sellOrderGoods']) {
        SaleOrderGoods _goods = SaleOrderGoods.fromJson(item);
        if (item["category_attr"] != null && item["category_attr"].length > 0) {
          for (var prop in item["category_attr"]) {
            _goods.category_attr.add(CategoryAttr.fromJson(prop));
          }
        }
        _goods.controllerr = TextEditingController();
        _goods.controllerr.text = _goods.num.toString();

        createOrderGoodsList.add(_goods);
      }
    });
    //搜索获取用户详细信息
    await HttpManager.getInstance().get("/customer/view?id=" + createSaleOrder.customer_id.toString(), cache: 0).then((e) {
      if (e["code"] == 0) {
        createCustomer = Customer.fromJson(e['data']);
      }
    });
    cashController = TextEditingController();
    cashController.text = createSaleOrder.cash.toString();
    posController = TextEditingController();
    posController.text = createSaleOrder.pos.toString();
    discountController = TextEditingController();
    discountController.text = createSaleOrder.shop_discount_amount;
    notifyListeners();
  }

  setDiscount(String _discount) {
    //检查用户如果是 批发商 就使用商品的批发价格
    createSaleOrder.shop_discount_amount = 100;
    if (createCustomer.wholesaler_type.toString() == "0" || createCustomer.wholesaler_type.toString() == "") {
      createSaleOrder.shop_discount_amount = double.parse(_discount);
    }

    calculatePrice();
  }

  updateWholesalerPrice() {
    if (createCustomer.wholesaler_type == "1") {
      for (int i = 0; i < createOrderGoodsList.length; i++) {
        createOrderGoodsList[i].sale_price = createOrderGoodsList[i].wholesaler_price1 ?? createOrderGoodsList[i].origin_sale_price;
      }
    } else if (createCustomer.wholesaler_type == "2") {
      for (int i = 0; i < createOrderGoodsList.length; i++) {
        createOrderGoodsList[i].sale_price = createOrderGoodsList[i].wholesaler_price2 ?? createOrderGoodsList[i].origin_sale_price;
      }
    } else {
      for (int i = 0; i < createOrderGoodsList.length; i++) {
        createOrderGoodsList[i].sale_price = createOrderGoodsList[i].origin_sale_price;
      }
    }
  }

  calculatePrice() {
    //试探用户是不是批发商 修改销售价格
    updateWholesalerPrice();
    createSaleOrder.goods_amount = 0;
    for (SaleOrderGoods _goods in createOrderGoodsList) {
      createSaleOrder.goods_amount += double.parse(_goods.sale_price) * _goods.num;
    }
    createSaleOrder.tax_amount = ((createSaleOrder.goods_amount * int.parse(App.sysConfig["taxRate"].toString())) / 100);

    createSaleOrder.user_discount_amount = createSaleOrder.goods_amount * (100 - createSaleOrder.user_discount_number) / 100;
    createSaleOrder.discount_amount = double.parse(createSaleOrder.shop_discount_amount.toString()) + double.parse(createSaleOrder.user_discount_amount.toString());
    createSaleOrder.order_amount = ((createSaleOrder.goods_amount + createSaleOrder.tax_amount - createSaleOrder.discount_amount) * 100).round() / 100;
    createSaleOrder.pos = createSaleOrder.order_amount - double.parse(createSaleOrder.cash.toString());
    notifyListeners();
  }

  setCash(String _cash) {
    createSaleOrder.cash = double.parse(_cash);
    calculatePrice();
  }

  confirmOrder() async {
    await saveOrder();
    await HttpManager.getInstance().post("/sell/confirm?id=${createSaleOrder.id.toString()}&forceConfirm=1", cache: 0).then((e) {
      if (e['code'] == 0) {
        createNewOrder();
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
      } else {
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.white,
          textColor: Colors.red,
          fontSize: 15.0,
        );
      }
    });
  }

  saveOrder() async {
    List<Map<String, dynamic>> goodsListJson = [];

    for (var e in createOrderGoodsList) {
      goodsListJson.add(e.toJson());
    }

    String url = (createSaleOrder.id > 0) ? "/sell/update?id=" + createSaleOrder.id.toString() : "/sell/insert";

    await HttpManager.getInstance().post(
      url,
      cache: 0,
      params: {"sellOrder": createSaleOrder.toJson(), "sellOrderGoods": goodsListJson},
    ).then((e) {
      if (e['code'] == 0) {
        if (createSaleOrder.id <= 0) {
          setCreateSaleOrder(e['data']);
        }

        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
      } else {
        Fluttertoast.showToast(
          msg: e["msg"],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.white,
          textColor: Colors.red,
          fontSize: 15.0,
        );
      }
    });
  }
}

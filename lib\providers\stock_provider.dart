import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../model/ResponseListModel.dart';
import '../model/StockGoods.dart';
import '../service/HttpManager.dart';

class StockProvider with ChangeNotifier {
  List<StockGoods> goodsList = [];
  int page = 1;

  String searchGoodsName = "";
  String searchCode = "";
  String searchSku = "";

  Future<bool> loadList(int _storeId) async {
    page = 1;
    goodsList = [];
    return fetchList(_storeId);
  }

  Future<bool> fetchList(int _storeId) async {
    //&store_id=1
    return await HttpManager.getInstance()
        .get(
            "/stock/list?pageNum=$page&filters[store_id]=${_storeId.toString()}&filters[goods_name]=$searchGoodsName&filters[sku]=$searchSku&filters[code]=$searchCode",
            cache: 0)
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        return false;
      }
      for (var item in responseListModel.list) {
        var tempItem = StockGoods.fromJson(item);
        // if (item["category_attr"] != null && item["category_attr"].length > 0) {
        //   for (var prop in item["category_attr"]) {
        //     print(item["category_attr"].toString());
        //     print("-----------------");
        //     tempCateAttr.add(CategoryAttr.fromJson(prop));
        //   }
        //   tempItem.category_attr = tempCateAttr;
        // }

        //List<CategoryAttr> tempCateAttr = CategoryAttr.fromJson(item["category_attr"]);
        //print(tempItem.category_attr);
        goodsList.add(tempItem);
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList(int _storeId) async {
    page++;
    return fetchList(_storeId);
  }
}

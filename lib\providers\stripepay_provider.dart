import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';

class StripePayProvider with ChangeNotifier {
  //初始化状态
  var isInitialized = Terminal.isInitialized;

  //选择的地址
  Location? selectedLocation;

  //连接设备状态
  ConnectionStatus connectionStatus = ConnectionStatus.notConnected;

  //连接设备
  Reader? reader;

  setInitStatus(bool init) {
    isInitialized = init;
    notifyListeners();
  }

  setReader(Reader? reader) {
    reader = reader;
    notifyListeners();
  }

  setLocation(Location? location) {
    selectedLocation = location;
    notifyListeners();
  }

  setConnectionStatus(ConnectionStatus connectionStatus) {
    connectionStatus = connectionStatus;
    notifyListeners();
  }
}

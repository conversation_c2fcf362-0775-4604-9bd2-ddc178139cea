import 'package:flutter/material.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';

import '../main.dart';
import '../model/ResponseListModel.dart';
import '../model/StockGoods.dart';
import '../model/TransferOrder.dart';
import '../model/TransferOrderGoods.dart';

class TransferProvider with ChangeNotifier {
  TransferOrder transferOrder = TransferOrder();
  List<TransferOrder> orderList = [];
  List<TransferOrderGoods> orderGoodsList = [];
  int page = 1;
  String orderNo = "";
  Future<bool> loadList() async {
    page = 1;
    orderList = [];
    return fetchList();
  }

  Future<bool> fetchList() async {
    //&store_id=1

    return await HttpManager.getInstance()
        .get(
      "/transfer-order/list?pageNum=" +
          page.toString() +
          "&filters[to_store_id]=" +
          App.adminInfo.store_id.toString() +
          "&filters[order_no]=" +
          orderNo,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      var responseListModel = ResponseListModel.fromJson(e["data"]);
      if (responseListModel.list.isEmpty) {
        notifyListeners();
        return false;
      }
      for (var item in responseListModel.list) {
        orderList.add(TransferOrder.fromJson(item));
      }

      notifyListeners();
      return true;
    });
  }

  Future<bool> fetchNextList() async {
    page++;
    return fetchList();
  }

  getDetail(id) async {
    return await HttpManager.getInstance()
        .get(
      "/transfer-order/view?id=" + id,
      cache: 0,
    )
        .then((e) {
      //获取到数据  就刷数据
      //var responseListModel = ResponseListModel.fromJson(e["data"]);
      orderGoodsList = [];
      transferOrder = TransferOrder();
      transferOrder = TransferOrder.fromJson(e["data"]["transferOrder"]);
      // TransferOrderGoods transferOrderGoods = TransferOrderGoods.fromJson(e["data"]["transferOrderGoods"]);
      for (var item in e["data"]["transferOrderGoods"]) {
        var tempItem = TransferOrderGoods.fromJson(item);
        List<CategoryAttr> tempCateAttr = [];
        if (item["category_attr"] != null && item["category_attr"].length > 0) {
          for (var prop in item["category_attr"]) {
            tempCateAttr.add(CategoryAttr.fromJson(prop));
          }
          print(tempCateAttr);
          tempItem.category_attr = tempCateAttr;
        } else {
          tempItem.category_attr = tempCateAttr;
        }
        orderGoodsList.add(tempItem);
      }

      notifyListeners();
    });
  }
}

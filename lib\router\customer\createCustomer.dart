import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/providers/customer_provider.dart';
import 'package:tattooerp20260622/router/sale/createSale.dart';

class CreateCustomer extends StatefulWidget {
  const CreateCustomer({Key? key}) : super(key: key);
  @override
  _CreateCustomer createState() => _CreateCustomer();
}

class _CreateCustomer extends State<CreateCustomer> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _telController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _remarkController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: const Text(
          "Create Customer",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Color(0xff003473),
          ),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
        child: ListView(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 150, child: Text("Customer Name")),
                Expanded(
                  child: SizedBox(
                    height: 38,
                    child: TextField(
                      controller: _nameController,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 150, child: Text("Tel")),
                Expanded(
                  child: SizedBox(
                    height: 38,
                    child: TextField(
                      controller: _telController,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 150, child: Text("Email")),
                Expanded(
                  child: SizedBox(
                    height: 38,
                    child: TextField(
                      controller: _emailController,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 150, child: Text("Remark")),
                Expanded(
                  child: SizedBox(
                    height: 38,
                    child: TextField(
                      controller: _remarkController,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            ElevatedButton(
              onPressed: () async {
                context.read<CustomerProvider>().setCustomer(
                      customerName: _nameController.text,
                      tel: _telController.text,
                      email: _emailController.text,
                      remark: _remarkController.text,
                    );

                bool asd = await context
                    .read<CustomerProvider>()
                    .createCustomer(context);

                if (asd) {
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const CreateSale()));
                }
              },
              child: const Text("Create"),
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all(
                  const Color(0xff003473),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

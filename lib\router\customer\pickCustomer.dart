import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/model/Customer.dart';
import 'package:tattooerp20260622/providers/customer_provider.dart';
import 'package:tattooerp20260622/providers/saleorder_provider.dart';

class PickCustomer extends StatefulWidget {
  const PickCustomer({Key? key}) : super(key: key);
  @override
  _PickCustomer createState() => _PickCustomer();
}

class _PickCustomer extends State<PickCustomer> {
  @override
  void initState() {
    super.initState();
    context.read<CustomerProvider>().loadList();
    _userCheckedCate = context
        .read<SaleOrderProvider>()
        .createSaleOrder
        .customer_id
        .toString();
    _searchController.text = context.read<CustomerProvider>().searchTel;
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    _refreshController.dispose();
  }

  final _searchController = TextEditingController();
  bool loading = false;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    bool loadStatus = await context.read<CustomerProvider>().loadList();
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<CustomerProvider>().fetchNextList();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  var _userCheckedCate = "0";
  Widget createItem(Customer _customer) {
    return RadioListTile(
      value: _customer.id.toString(),
      groupValue: _userCheckedCate,
      title: Text(_customer.customerName),
      subtitle: Text(_customer.tel),
      controlAffinity: ListTileControlAffinity.trailing,
      onChanged: (value) {
        print(_customer);
        print(_customer.discount_number);
        setState(() {
          _userCheckedCate = value.toString();
          context.read<SaleOrderProvider>().createSaleOrder.customer_id =
              _userCheckedCate;
          context.read<SaleOrderProvider>().createSaleOrder.customer_name =
              _customer.customerName;
          context
                  .read<SaleOrderProvider>()
                  .createSaleOrder
                  .user_discount_number =
              int.parse((_customer.discount_number ?? 100).toString());
          context.read<SaleOrderProvider>().setCustomer(_customer);
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
          elevation: 3.0,
          iconTheme: const IconThemeData(color: Color(0xff003473)),
          backgroundColor: const Color(0xfff7f9fb),
          centerTitle: true,
          automaticallyImplyLeading: true,
          title: const Text(
            "Pick A Customer",
            style: TextStyle(color: Color(0xff003473)),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(top: 13, bottom: 13, right: 15),
              child: CupertinoButton(
                padding:
                    const EdgeInsets.only(top: 0, bottom: 0, right: 9, left: 9),
                child: const Text("Confirm", style: TextStyle(fontSize: 15)),
                color: Color(0xffcc003f),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          ]),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.transparent,
                        width: 0.0,
                      ),
                      color: Colors.grey[200],
                      borderRadius:
                          const BorderRadius.all(Radius.circular(7.0)),
                    ),
                    alignment: Alignment.center,
                    height: 34,
                    child: TextFormField(
                      controller: _searchController,
                      style: const TextStyle(fontSize: 16.0),
                      decoration: InputDecoration(
                        hintText: "Customer info",
                        contentPadding:
                            const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                        fillColor: Colors.transparent,
                        filled: true,
                        border: InputBorder.none,
                        prefixIcon: const Icon(Icons.search,
                            color: Colors.black54, size: 21),
                        suffixIcon: IconButton(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          icon: const Icon(Icons.delete_outlined,
                              color: Colors.black26, size: 21),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              context.read<CustomerProvider>().searchTel = "";
                              context.read<CustomerProvider>().loadList();
                            });
                          },
                        ),
                        focusedBorder: const OutlineInputBorder(
                            borderSide: BorderSide(
                                color: Colors.transparent, width: 0)),
                      ),
                      keyboardType: TextInputType.text,
                      autofocus: false,
                    ),
                  ),
                ),
                TextButton(
                    onPressed: () {
                      context.read<CustomerProvider>().searchTel =
                          _searchController.text;
                      context.read<CustomerProvider>().loadList();
                    },
                    child: const Text("Search")),
              ],
            ),
          ),
          Expanded(
            child: SmartRefresher(
              enablePullDown: true,
              enablePullUp: true,
              header: const WaterDropHeader(),
              footer: CustomFooter(
                builder: (BuildContext _context, LoadStatus? mode) {
                  Widget body;
                  if (mode == LoadStatus.idle) {
                    body = const Text("Load More");
                  } else if (mode == LoadStatus.loading) {
                    body = const CupertinoActivityIndicator();
                  } else if (mode == LoadStatus.failed) {
                    body = const Text("加载失败！点击重试！");
                  } else if (mode == LoadStatus.canLoading) {
                    body = const Text("Release Now");
                  } else if (mode == LoadStatus.noMore) {
                    body = const Text("No More Data");
                  } else {
                    body = const Text("No More Data");
                  }
                  return SizedBox(
                    height: 55.0,
                    child: Center(child: body),
                  );
                },
              ),
              controller: _refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              child: ListView(
                children: context
                    .watch<CustomerProvider>()
                    .customerList
                    .map((e) => createItem(e))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

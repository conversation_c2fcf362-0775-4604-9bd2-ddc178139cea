import 'package:common_utils/common_utils.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/model/InstockOrder.dart';
import 'package:tattooerp20260622/providers/instock_provider.dart';
import 'package:tattooerp20260622/utils/util.dart';

import 'InstockDetail.dart';

class Instock extends StatefulWidget {
  const Instock({
    Key? key,
  }) : super(key: key);

  @override
  _InstockList createState() => _InstockList();
}

class _InstockList extends State<StatefulWidget> {
  bool loading = false;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    bool loadStatus = await context.read<InstockProvider>().loadList();
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<InstockProvider>().fetchNextList();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<InstockProvider>().loadList();
    _searchController.addListener(() {
      setState(() {
        _showClearButton = _searchController.text.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    _refreshController.dispose();
  }

  Widget createOrderItem(InstockOrder _order) {
    // 获取状态颜色
    Color statusColor = _order.order_status == 0 
        ? const Color(0xffef4444) // 红色 - 待处理
        : const Color(0xff10b981); // 绿色 - 已处理
    
    String statusText = _order.order_status == 0 ? "Pending Inbound" : "Inbounded";
    
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => InstockDetail(id: _order.id.toString()),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.06),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 状态指示条
            Container(
              height: 4,
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            
            // 主要内容区域
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 供应商名称和状态
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _order.supplier_name ?? "Unknown Supplier",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xff1f2937),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: statusColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                statusText,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xff6366f1).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _order.order_no,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Color(0xff6366f1),
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            Utils.transTime(_order.createTime ?? ""),
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xff9ca3af),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 分割线
                  Container(
                    height: 1,
                    color: const Color(0xfff1f5f9),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 订单金额
                  Row(
                    children: [
                      Icon(
                        Icons.attach_money_rounded,
                        size: 18,
                        color: const Color(0xff059669),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        "Order Amount: ",
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xff6b7280),
                        ),
                      ),
                      Text(
                        "\$${_order.order_amount}",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: Color(0xff059669),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  final _searchController = TextEditingController();
  bool _showClearButton = false;
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
          elevation: 0,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xff6366f1), Color(0xff8b5cf6)],
              ),
            ),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
          centerTitle: true,
          automaticallyImplyLeading: true,
          title: Container(
            height: 42,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.95),
              borderRadius: BorderRadius.circular(21),
              border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    textAlignVertical: TextAlignVertical.center,
                    style: const TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                    decoration: InputDecoration(
                      hintText: "Search Order No.",
                      hintStyle: TextStyle(
                        fontSize: 15,
                        color: Colors.grey[500],
                        fontWeight: FontWeight.w400,
                      ),
                      prefixIcon: Icon(
                        Icons.search_rounded,
                        color: Colors.grey[600],
                        size: 22,
                      ),
                      suffixIcon: _showClearButton
                           ? IconButton(
                               icon: Icon(
                                 Icons.clear_rounded,
                                 color: Colors.grey[600],
                                 size: 20,
                               ),
                               onPressed: () {
                                 setState(() {
                                   _searchController.clear();
                                   context.read<InstockProvider>().searchOrderNo = "";
                                   context.read<InstockProvider>().loadList();
                                 });
                               },
                             )
                           : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                    ),
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.search,
                    onSubmitted: (value) {
                      context.read<InstockProvider>().searchOrderNo = value;
                      context.read<InstockProvider>().loadList();
                    },
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: Material(
                    color: const Color(0xff6366f1),
                    borderRadius: BorderRadius.circular(18),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(18),
                      onTap: () {
                        context.read<InstockProvider>().searchOrderNo =
                            _searchController.text;
                        context.read<InstockProvider>().loadList();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: const Icon(
                          Icons.search_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext _context, LoadStatus? mode) {
            Widget body;
            LogUtil.e("data = ${mode}");
            if (mode == LoadStatus.idle) {
              body = const Text("Load More");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release Now");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("No More Data");
            } else {
              body = const Text("No More Data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView(
          children: context
              .watch<InstockProvider>()
              .orderList
              .map((e) => createOrderItem(e))
              .toList(),
        ),
      ),
    );
  }
}

import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/utils/util.dart';

import '../../model/InventoryOrder.dart';
import '../../providers/inventory_provider.dart';
import 'InventoryDetail.dart';

class Inventory extends StatefulWidget {
  const Inventory({
    Key? key,
  }) : super(key: key);

  @override
  _Inventory createState() => _Inventory();
}

class _Inventory extends State<StatefulWidget> {
  bool loading = false;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    bool loadStatus = await context.read<InventoryProvider>().loadList();
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<InventoryProvider>().fetchNextList();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<InventoryProvider>().loadList();
    _searchController.addListener(() {
      setState(() {
        _showClearButton = _searchController.text.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    _refreshController.dispose();
  }

  Widget createOrderItem(InventoryOrder _order) {
    // 根据订单状态确定状态颜色
    Color statusColor;
    String statusText;
    
    if (_order.order_status == 0) {
      statusColor = const Color(0xffE74C3C);
      statusText = "待盘点";
    } else {
      statusColor = const Color(0xff27AE60);
      statusText = "已完成";
    }
    
    return GestureDetector(
      onTap: () {
        context.read<InventoryProvider>().skuCount = 0;
        context.read<InventoryProvider>().goodsNumber = 0;
        context.read<InventoryProvider>().inventory = InventoryOrder();
        context.read<InventoryProvider>().orderGoodsList = [];

        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => InventoryDetail(id: _order.id.toString()),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：店铺名称和状态
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  // 状态指示器
                  Container(
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 店铺名称
                  Expanded(
                    child: Text(
                      _order.store_name ?? "Unknown Store",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff2C3E50),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // 状态标签
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 中间：详细信息
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 订单号和创建时间信息
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          "订单号",
                          _order.order_no,
                          Icons.receipt_long,
                          const Color(0xff3498DB),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoItem(
                          "创建时间",
                          Utils.transTime(_order.createTime ?? ""),
                          Icons.access_time,
                          const Color(0xff9B59B6),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 创建人信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        "创建人",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xff7F8C8D),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xff1ABC9C).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.person,
                              size: 16,
                              color: const Color(0xff1ABC9C),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              _order.add_user_name ?? "Unknown",
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(0xff1ABC9C),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: color.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xff2C3E50),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  final _searchController = TextEditingController();
  bool _showClearButton = false;
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      appBar: AppBar(
          elevation: 0,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xff6366f1), Color(0xff8b5cf6)],
              ),
            ),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
          centerTitle: true,
          automaticallyImplyLeading: true,
          title: Container(
            height: 42,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.95),
              borderRadius: BorderRadius.circular(21),
              border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    textAlignVertical: TextAlignVertical.center,
                    style: const TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                    decoration: InputDecoration(
                      hintText: "Search Order No.",
                      hintStyle: TextStyle(
                        fontSize: 15,
                        color: Colors.grey[500],
                        fontWeight: FontWeight.w400,
                      ),
                      prefixIcon: Icon(
                        Icons.search_rounded,
                        color: Colors.grey[600],
                        size: 22,
                      ),
                      suffixIcon: _showClearButton
                           ? IconButton(
                               icon: Icon(
                                 Icons.clear_rounded,
                                 color: Colors.grey[600],
                                 size: 20,
                               ),
                               onPressed: () {
                                 setState(() {
                                   _searchController.clear();
                                   context.read<InventoryProvider>().searchOrderNo = "";
                                   context.read<InventoryProvider>().loadList();
                                 });
                               },
                             )
                           : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                    ),
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.search,
                    onSubmitted: (value) {
                      context.read<InventoryProvider>().searchOrderNo = value;
                      context.read<InventoryProvider>().loadList();
                    },
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  child: Material(
                    color: const Color(0xff6366f1),
                    borderRadius: BorderRadius.circular(18),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(18),
                      onTap: () {
                        context.read<InventoryProvider>().searchOrderNo =
                            _searchController.text;
                        context.read<InventoryProvider>().loadList();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: const Icon(
                          Icons.search_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext _context, LoadStatus? mode) {
            Widget body;
            LogUtil.e("data = ${mode}");
            if (mode == LoadStatus.idle) {
              body = const Text("Load More");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release Now");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("No More Data");
            } else {
              body = const Text("No More Data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView(
          children: context
              .watch<InventoryProvider>()
              .orderList
              .map((e) => createOrderItem(e))
              .toList(),
        ),
      ),
    );
  }
}

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
// import 'package:sm_scan/shangmi_scan_mixin.dart';

import '../../model/InventoryGoods.dart';
import '../../providers/inventory_provider.dart';
import '../../service/HttpManager.dart';
import '../../utils/util.dart';

// ignore: must_be_immutable
class InventoryDetail extends StatefulWidget {
  InventoryDetail({
    Key? key,
    this.id,
  }) : super(key: key);
  String? id;
  @override
  State<StatefulWidget> createState() {
    return _InventoryDetail();
  }
}

class _InventoryDetail extends State<InventoryDetail> {
  bool loading = false;

  @override
  void initState() {
    super.initState();
    context.read<InventoryProvider>().getDetail(widget.id);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget createOrderGoodsItem(InventoryGoods _goods) {
    return Container(
      margin: const EdgeInsets.fromLTRB(10, 5, 10, 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: const Color(0xFFE8E8E8),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 左侧状态指示条
          Container(
            width: 4,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF4A90E2),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          // 主要内容区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 商品名称
                  Text(
                    _goods.goods_name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1A1A1A),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // SKU和Code信息
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4A90E2).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: const Color(0xFF4A90E2).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          "SKU: ${_goods.sku ?? ""}",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF4A90E2),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: const Color(0xFF10B981).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          "Code: ${_goods.code ?? ""}",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF10B981),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // 库存信息
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            const Text(
                              "Inventory: ",
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF666666),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _goods.num.toString(),
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFFFF6B6B),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Row(
                          children: [
                            const Text(
                              "Stock: ",
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF666666),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _goods.stock_num.toString(),
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFF10B981),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: Text(
          context.watch<InventoryProvider>().inventory.order_no,
          style: const TextStyle(color: Colors.black),
        ),
      ),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.fromLTRB(10, 5, 10, 5),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: const Color(0xFFE8E8E8),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 120,
                  decoration: BoxDecoration(
                    color: context
                                .watch<InventoryProvider>()
                                .inventory
                                .order_status ==
                            0
                        ? const Color(0xFFFF6B6B)
                        : const Color(0xFF10B981),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 第一行：状态和订单信息
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Text(
                                      "Status: ",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF666666),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Utils.orderStatus(
                                      context
                                          .watch<InventoryProvider>()
                                          .inventory
                                          .order_status,
                                      context
                                          .watch<InventoryProvider>()
                                          .inventory
                                          .status_name,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                TextButton(
                                  onPressed: () {
                                    HttpManager.getInstance()
                                        .get(
                                            "/inventory/search-and-insert?code=3332234&order_id=${context.read<InventoryProvider>().inventory.id}",
                                            cache: 0)
                                        .then((e) {
                                      print(e);
                                      if (e['code'] == "0" || e['code'] == 0) {
                                        Fluttertoast.showToast(
                                          msg: e['msg'],
                                          toastLength: Toast.LENGTH_SHORT,
                                          gravity: ToastGravity.BOTTOM,
                                          timeInSecForIosWeb: 1,
                                          backgroundColor: Colors.black87,
                                          textColor: Colors.white,
                                          fontSize: 15.0,
                                        );
                                        context
                                            .read<InventoryProvider>()
                                            .pushGoods(
                                                e['data']['flag'] == "cunzai"
                                                    ? true
                                                    : false,
                                                InventoryGoods.fromJson(
                                                    e['data']['inventoryGoods']));
                                      } else {
                                        Fluttertoast.showToast(
                                          msg: e['msg'],
                                          toastLength: Toast.LENGTH_SHORT,
                                          gravity: ToastGravity.BOTTOM,
                                          timeInSecForIosWeb: 1,
                                          backgroundColor: Colors.red,
                                          textColor: Colors.white,
                                          fontSize: 15.0,
                                        );
                                      }
                                    });
                                  },
                                  child: const Text("Test Add"),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFF6B6B).withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: const Color(0xFFFF6B6B).withOpacity(0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    context
                                        .watch<InventoryProvider>()
                                        .inventory
                                        .order_no,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFFFF6B6B),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  Utils.transTime(context
                                          .watch<InventoryProvider>()
                                          .inventory
                                          .createTime ??
                                      ""),
                                  style: const TextStyle(
                                    color: Color(0xFF888888),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // 分隔线
                        Container(
                          height: 1,
                          color: const Color(0xFFE8E8E8),
                        ),
                        const SizedBox(height: 16),
                        // 第二行：统计信息
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  const Text(
                                    "SKU Count",
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF666666),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    context.watch<InventoryProvider>().skuCount.toString(),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      color: Color(0xFF4A90E2),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 30,
                              color: const Color(0xFFE8E8E8),
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  const Text(
                                    "Total Products",
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF666666),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    context
                                        .watch<InventoryProvider>()
                                        .goodsNumber
                                        .toString(),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      color: Color(0xFF10B981),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              children: context
                  .watch<InventoryProvider>()
                  .orderGoodsList
                  .map((e) => createOrderGoodsItem(e))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> shangmiCodeHandle(String _code) async {
    if (context.read<InventoryProvider>().inventory.order_status > 1) {
      //提示订单已完成,不能再操作
      Fluttertoast.showToast(
        msg: "Inventory Order Was Completed",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 15.0,
      );
      return;
    }

    //对比当前商品列表 是不是存在对应的sku，存在就添加数量，不存在就提示
    HttpManager.getInstance()
        .get(
            "/inventory/search-and-insert?code=$_code&order_id=${context.read<InventoryProvider>().inventory.id}",
            cache: 0)
        .then((e) {
      if (e['code'] == "0" || e['code'] == 0) {
        Fluttertoast.showToast(
          msg: e['msg'],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black87,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        //根据返回时新建 还是 追加 使用不同操作
        context.read<InventoryProvider>().pushGoods(
            e['data']['flag'] == "cunzai" ? true : false,
            InventoryGoods.fromJson(e['data']['inventoryGoods']));
      } else {
        Fluttertoast.showToast(
          msg: e['msg'],
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 15.0,
        );
      }
    });
  }
}

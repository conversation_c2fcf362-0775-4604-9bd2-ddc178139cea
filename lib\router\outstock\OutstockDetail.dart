import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// import 'package:sm_scan/shangmi_scan_mixin.dart';
import 'package:tattooerp20260622/model/OutStockGoods.dart';
import 'package:tattooerp20260622/providers/outstock_provider.dart';

import '../../utils/util.dart';

// ignore: must_be_immutable
class OutstockDetail extends StatefulWidget {
  OutstockDetail({
    Key? key,
    this.id,
  }) : super(key: key);
  String? id;
  @override
  State<StatefulWidget> createState() {
    return _OutstockDetail();
  }
}

class _OutstockDetail extends State<OutstockDetail> {
  bool loading = false;

  @override
  void initState() {
    super.initState();
    context.read<OutStockProvider>().getDetail(widget.id);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget createOrderGoodsItem(OutStockGoods _goods) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Color(0xfff8fafc)],
        ),
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _goods.goods_name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff1f2937),
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      "Sku: " + (_goods.sku ?? ""),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff6b7280),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "Code: " + (_goods.code ?? ""),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff6b7280),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 5),
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _goods.category_attr.isNotEmpty
                      ? _goods.category_attr.map((e) {
                          return Text((e.name ?? "") +
                              ": " +
                              (e.value?.toString() ?? ""));
                        }).toList()
                      : [],
                ),
              ),
            ],
          ),
          DottedBorder(
            customPath: (size) {
              return Path()
                ..moveTo(0, 10)
                ..lineTo(size.width, 10);
            },
            dashPattern: const [8, 4],
            color: Colors.black26,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                children: [
                  const Text("Need: "),
                  Text(
                    _goods.num.toString(),
                    style:
                        const TextStyle(fontSize: 15, color: Color(0xffcc003f)),
                  ),
                ],
              ),
              context.watch<OutStockProvider>().outStockOrder.order_status == 1
                  ? Row(
                      children: [
                        const Text(
                          "Actual:",
                        ),
                        Text(
                          _goods.out_num.toString(),
                          style: const TextStyle(
                              fontSize: 15, color: Color(0xffcc003f)),
                        )
                      ],
                    )
                  : Row(
                      children: [
                        const Text("Actual: "),
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xff6366f1), Color(0xff8b5cf6)],
                            ),
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            color: Colors.white,
                            iconSize: 16,
                            onPressed: () {
                              _goods.controllerr.text =
                                  (int.parse(_goods.controllerr.text) - 1 >= 0
                                          ? int.parse(_goods.controllerr.text) -
                                              1
                                          : 0)
                                      .toString();
                              _goods.out_num = _goods.controllerr.text;
                            },
                            icon: const Icon(CupertinoIcons.minus),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 60,
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: const Color(0xffe5e7eb)),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                spreadRadius: 1,
                                blurRadius: 4,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: TextField(
                            style: const TextStyle(
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              color: Color(0xff374151),
                            ),
                            controller: _goods.controllerr,
                            textAlign: TextAlign.center,
                            textAlignVertical: TextAlignVertical.center,
                            readOnly: true,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                              isDense: true,
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xff6366f1), Color(0xff8b5cf6)],
                            ),
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            color: Colors.white,
                            iconSize: 16,
                            onPressed: () {
                              _goods.controllerr.text =
                                  (int.parse(_goods.controllerr.text) + 1)
                                      .toString();
                              _goods.out_num = _goods.controllerr.text;
                              //context.read<InstockProvider>().plusGoodsNum(_goods.id);
                            },
                            icon: const Icon(CupertinoIcons.plus),
                          ),
                        ),
                      ],
                    ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xff6366f1), Color(0xff8b5cf6)],
            ),
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: Text(
          context.watch<OutStockProvider>().outStockOrder.order_no,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Color(0xfff8fafc)],
              ),
              borderRadius: const BorderRadius.all(Radius.circular(16)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  spreadRadius: 1,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context
                                    .watch<OutStockProvider>()
                                    .outStockOrder
                                    .customer_name ??
                                "",
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xff1f2937),
                              height: 1.4,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            Utils.transTime(context
                                .watch<OutStockProvider>()
                                .outStockOrder
                                .createTime),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xff6b7280),
                              fontWeight: FontWeight.w500,
                              height: 1.4,
                            ),
                          )
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Utils.orderStatus(
                            context
                                .watch<OutStockProvider>()
                                .outStockOrder
                                .order_status,
                            context
                                .watch<OutStockProvider>()
                                .outStockOrder
                                .status_name),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              children: context
                  .watch<OutStockProvider>()
                  .orderGoodsList
                  .map((e) => createOrderGoodsItem(e))
                  .toList(),
            ),
          ),
          context.watch<OutStockProvider>().outStockOrder.order_status == 0
              ? Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const SizedBox(width: 12),
                      Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Color(0xff10b981), Color(0xff059669)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: CupertinoButton(
                            color: Colors.transparent,
                            padding: const EdgeInsets.fromLTRB(24, 12, 24, 12),
                            child: const Text(
                              "Save",
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            onPressed: () {
                              context.read<OutStockProvider>().saveOrder();
                            }),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Color(0xffef4444), Color(0xffdc2626)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: CupertinoButton(
                            color: Colors.transparent,
                            padding: const EdgeInsets.fromLTRB(24, 12, 24, 12),
                            child: const Text(
                              "Confirm",
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            onPressed: () async {
                              await showDialog<bool>(
                                context: context,
                                builder: (context) {
                                  return AlertDialog(
                                    title: const Text("Message"),
                                    titlePadding: EdgeInsets.all(10),
                                    titleTextStyle: const TextStyle(
                                      color: Colors.black87,
                                      fontSize: 16,
                                    ),
                                    //中间显示的内容
                                    content: const Text("Confirm?"),
                                    contentPadding: const EdgeInsets.all(10),
                                    //中间显示内容的文本样式
                                    contentTextStyle: const TextStyle(
                                        color: Colors.black54, fontSize: 14),
                                    //底部按钮区域
                                    actions: <Widget>[
                                      TextButton(
                                        child: const Text("Cancel"),
                                        onPressed: () {
                                          Navigator.of(context).pop(false);
                                        },
                                      ),
                                      TextButton(
                                        child: const Text(
                                          "Confirm",
                                          style: TextStyle(color: Colors.red),
                                        ),
                                        onPressed: () {
                                          //关闭 返回true
                                          Navigator.of(context).pop(true);
                                          context
                                              .read<OutStockProvider>()
                                              .confirmOrder();
                                        },
                                      ),
                                    ],
                                  );
                                },
                              );
                              //context.watch<InstockProvider>().confirmOrder();
                            }),
                      ),
                    ],
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  Future<void> shangmiCodeHandle(String _code) async {
    if (context.read<OutStockProvider>().outStockOrder.order_status == 1) {
      return;
    }
    //对比当前商品列表 是不是存在对应的sku，存在就添加数量，不存在就提示
    context.read<OutStockProvider>().addGoodsNum(_code);
  }
}

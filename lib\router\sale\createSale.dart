import 'dart:async';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/main.dart';
import 'package:tattooerp20260622/model/SaleOrderGoods.dart';
import 'package:tattooerp20260622/providers/saleorder_provider.dart';
import 'package:barcode_newland_flutter/newland_scan_result.dart';
import 'package:barcode_newland_flutter/newland_scanner.dart';
import 'package:tattooerp20260622/router/stripe_pay/payment.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';
import 'package:tattooerp20260622/utils/state_tools.dart';
import '../customer/createCustomer.dart';
import '../customer/pickCustomer.dart';

class CreateSale extends StatefulWidget {
  const CreateSale({super.key});

  @override
  _CreateSale createState() => _CreateSale();
}

class _CreateSale extends State<CreateSale> with StateTools {
  late Stream<NewlandScanResult> _stream;
  StreamSubscription<NewlandScanResult>? _subscription;

  void onScanResultReceived(NewlandScanResult result) {
    // 处理扫描结果的逻辑
    context.read<SaleOrderProvider>().searchGoods(result.barcodeData);
    print('Received scan result: $result');
  }
  void initStreamListener() {
    // 取消之前的监听（如果存在）
    _subscription?.cancel();

    // 添加新的监听
    _subscription = _stream.listen((NewlandScanResult result) {
      // 当流发出新数据时触发
      onScanResultReceived(result);
    }, onError: (error) {
      // 处理错误
      print('Error in  NewlandScanResult stream: $error');
    }, onDone: () {
      // 流关闭时触发
      print('NewlandScanResult Stream closed');
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  Widget createOrderGoodsItem(SaleOrderGoods _goods) {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.fromLTRB(0, 3, 0, 3),
      padding: const EdgeInsets.all(10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(_goods.goods_name, style: const TextStyle(fontSize: 16)),
                    Text("Sku: ${_goods.sku}", style: const TextStyle()),
                    Text("Code: ${_goods.code}", style: const TextStyle()),
                  ],
                ),
              ),
              const SizedBox(width: 5),
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _goods.category_attr.isNotEmpty
                      ? _goods.category_attr.map((e) {
                          return Text((e.name ?? "") + ": " + (e.value ?? ""));
                        }).toList()
                      : [],
                ),
              ),
            ],
          ),
          DottedBorder(
            customPath: (size) {
              return Path()
                ..moveTo(0, 10)
                ..lineTo(size.width, 10);
            },
            dashPattern: const [8, 4],
            color: Colors.black26,
            child: Padding(padding: const EdgeInsets.all(8.0), child: Container()),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              context.watch<SaleOrderProvider>().createSaleOrder.order_status == 1
                  ? Row(
                      children: [
                        Text("\$${_goods.sale_price}"),
                        const SizedBox(width: 10),
                        const Text("*"),
                        const SizedBox(width: 10),
                        Text(_goods.num.toString()),
                        const SizedBox(width: 10),
                        const Text("="),
                        const SizedBox(width: 10),
                        Text("\$${double.parse(_goods.sale_price) * int.parse(_goods.num.toString())}"),
                      ],
                    )
                  : Row(
                      children: [
                        Text("\$${_goods.sale_price}"),
                        const SizedBox(width: 10),
                        const Text("*"),
                        const SizedBox(width: 10),
                        CircleAvatar(
                          radius: 12,
                          backgroundColor: const Color(0xff003473),
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            color: Colors.white,
                            iconSize: 18,
                            onPressed: () {
                              _goods.controllerr.text = (int.parse(_goods.controllerr.text) - 1 >= 0 ? int.parse(_goods.controllerr.text) - 1 : 0).toString();
                              _goods.num = int.parse(_goods.controllerr.text);
                              context.read<SaleOrderProvider>().calculatePrice();
                            },
                            icon: const Icon(CupertinoIcons.minus),
                          ),
                        ),
                        const SizedBox(width: 4),
                        SizedBox(
                          width: 55,
                          height: 30,
                          child: TextField(
                            style: const TextStyle(height: 1.6),
                            controller: _goods.controllerr,
                            textAlign: TextAlign.center,
                            textAlignVertical: TextAlignVertical.center,
                            readOnly: true,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.all(3),
                              isDense: true,
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        CircleAvatar(
                          radius: 12,
                          backgroundColor: const Color(0xff003473),
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            color: Colors.white,
                            iconSize: 18,
                            onPressed: () {
                              _goods.controllerr.text = (int.parse(_goods.controllerr.text) + 1).toString();
                              _goods.num = int.parse(_goods.controllerr.text);
                              context.read<SaleOrderProvider>().calculatePrice();
                            },
                            icon: const Icon(CupertinoIcons.plus),
                          ),
                        ),
                        const SizedBox(width: 10),
                        const Text("="),
                        const SizedBox(width: 10),
                        Text("\$${double.parse(_goods.sale_price) * int.parse(_goods.num.toString())}"),
                      ],
                    ),
              context.watch<SaleOrderProvider>().createSaleOrder.order_status == 1
                  ? const SizedBox()
                  : SizedBox(
                      height: 25,
                      child: IconButton(
                        padding: const EdgeInsets.all(0),
                        onPressed: () {
                          context.read<SaleOrderProvider>().deleteGoods(int.parse(_goods.id.toString()));
                        },
                        icon: const Icon(CupertinoIcons.trash_fill, color: Colors.redAccent, size: 18),
                      ),
                    ),
            ],
          ),
        ],
      ),
    );
  }

  Widget customerInfoText(String _label, String _value) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            _label,
            style: const TextStyle(
              color: Colors.black54,
            ),
          ),
        ),
        Text(_value),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            if (App.adminInfo.store_id > 1) {
              Navigator.pushNamedAndRemoveUntil(
                context,
                "sellerNav",
                (Route<dynamic> route) => false,
              );
            } else {
              Navigator.pushNamedAndRemoveUntil(
                context,
                "keeperNav",
                (Route<dynamic> route) => false,
              );
            }
          },
        ),
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: Text(context.watch<SaleOrderProvider>().createSaleOrder.order_no != "" ? context.watch<SaleOrderProvider>().createSaleOrder.order_no : "Create Sale", textAlign: TextAlign.center, style: const TextStyle(color: Color(0xff003473))),
      ),
      body: ListView(
        children: [
          Container(
            margin: const EdgeInsets.all(10),
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(
                      height: 30,
                      child: Text(
                        "Customer",
                        style: TextStyle(
                          fontSize: 18,
                          color: Color(0xff003473),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 30,
                      child: context.watch<SaleOrderProvider>().createSaleOrder.order_status == 1
                          ? const SizedBox()
                          : ElevatedButton(
                              onPressed: () {
                                Navigator.of(context).push(MaterialPageRoute(
                                  builder: (context) => CreateCustomer(),
                                ));
                              },
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  const Color(0xff003473),
                                ),
                              ),
                              child: const Text("Create", style: TextStyle(color: Color(0xffffffff))),
                            ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customerInfoText("Name:", context.watch<SaleOrderProvider>().createCustomer.customerName),
                        customerInfoText("Tel:", context.watch<SaleOrderProvider>().createCustomer.tel ?? ""),
                        customerInfoText("Email:", context.watch<SaleOrderProvider>().createCustomer.email ?? ""),
                        customerInfoText("Rank:", context.watch<SaleOrderProvider>().createCustomer.rank_name ?? ""),
                        customerInfoText("Discount:", "${context.watch<SaleOrderProvider>().createSaleOrder.user_discount_number}%"),
                        customerInfoText("wholesaler:", context.watch<SaleOrderProvider>().createCustomer.wholesaler_type_name ?? ""),
                      ],
                    ),
                    SizedBox(
                      height: 30,
                      child: context.watch<SaleOrderProvider>().createSaleOrder.order_status == 1
                          ? SizedBox()
                          : ElevatedButton(
                              onPressed: () {
                                Navigator.of(context).push(MaterialPageRoute(
                                  builder: (context) => const PickCustomer(),
                                ));
                              },
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  const Color(0xff003473),
                                ),
                              ),
                              child: const Text("Pick", style: TextStyle(color: Color(0xffffffff))),
                            ),
                    ),
                  ],
                ),
                DottedBorder(
                  customPath: (size) {
                    return Path()
                      ..moveTo(0, 10)
                      ..lineTo(size.width, 10);
                  },
                  dashPattern: const [8, 4],
                  color: Colors.black26,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const SizedBox(
                              width: 70,
                              child: Text(
                                "Product:",
                                style: TextStyle(
                                  color: Colors.black54,
                                  height: 1.9,
                                ),
                              ),
                            ),
                            Text(
                              "+ \$${context.watch<SaleOrderProvider>().createSaleOrder.goods_amount}",
                              style: const TextStyle(
                                height: 1.9,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const SizedBox(
                              width: 70,
                              child: Text(
                                "Tax:",
                                style: TextStyle(
                                  color: Colors.black54,
                                  height: 1.9,
                                ),
                              ),
                            ),
                            Text(
                              "+ \$${context.watch<SaleOrderProvider>().createSaleOrder.tax_amount}",
                              style: const TextStyle(
                                height: 1.9,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const SizedBox(
                              width: 70,
                              child: Text(
                                "Discount:",
                                style: TextStyle(
                                  color: Colors.black54,
                                  height: 1.9,
                                ),
                              ),
                            ),
                            Text(
                              "- \$${context.watch<SaleOrderProvider>().createSaleOrder.discount_amount}",
                              style: const TextStyle(
                                height: 1.9,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const SizedBox(
                                width: 70,
                                child: Text(
                                  "Amount: ",
                                  style: TextStyle(fontSize: 16, color: Colors.black54, height: 2.3, fontWeight: FontWeight.bold),
                                )),
                            Text(
                              " \$${context.watch<SaleOrderProvider>().createSaleOrder.order_amount}",
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, height: 2.2),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            const Text("Discount:  -\$", style: TextStyle(height: 2.1, color: Colors.black54)),
                            SizedBox(
                              width: 80,
                              height: 40,
                              child: TextField(
                                controller: context.read<SaleOrderProvider>().discountController,
                                decoration: const InputDecoration(
                                  contentPadding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                                ),
                                onEditingComplete: () {
                                  context.read<SaleOrderProvider>().setDiscount(context.read<SaleOrderProvider>().discountController.text);
                                  FocusManager.instance.primaryFocus?.unfocus();
                                },
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Text(
                              "Cash:  \$",
                              style: TextStyle(
                                height: 2.1,
                                color: Colors.black54,
                              ),
                            ),
                            SizedBox(
                                width: 80,
                                height: 40,
                                child: TextField(
                                    controller: context.read<SaleOrderProvider>().cashController,
                                    decoration: const InputDecoration(
                                      contentPadding: EdgeInsets.fromLTRB(5.0, 0.0, 5.0, 0.0),
                                    ),
                                    onEditingComplete: () {
                                      context.read<SaleOrderProvider>().setCash(context.read<SaleOrderProvider>().cashController.text);
                                      FocusManager.instance.primaryFocus?.unfocus();
                                    })),
                          ],
                        ),
                        Row(
                          children: [
                            const Text(
                              "Pos:  \$",
                              style: TextStyle(
                                height: 2.1,
                                color: Colors.black54,
                              ),
                            ),
                            const SizedBox(width: 5),
                            SizedBox(
                              width: 75,
                              height: 40,
                              child: Text(context.read<SaleOrderProvider>().createSaleOrder.pos.toString(), style: const TextStyle(height: 2.3, fontSize: 16)),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                DottedBorder(
                  customPath: (size) {
                    return Path()
                      ..moveTo(0, 10)
                      ..lineTo(size.width, 10);
                  },
                  dashPattern: const [8, 4],
                  color: Colors.black26,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(),
                  ),
                ),
                context.watch<SaleOrderProvider>().createSaleOrder.order_status == 1
                    ? SizedBox()
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          ElevatedButton(
                            onPressed: () async {
                              await context.read<SaleOrderProvider>().saveOrder();
                            },
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(const Color(0xff003473)),
                            ),
                            child: const Text("Save", style: TextStyle(color: Color(0xffffffff))),
                          ),
                          context.watch<SaleOrderProvider>().createSaleOrder.id > 0
                              ? ElevatedButton(
                                  onPressed: () async {
                                    //在弹出dialog之前 刷新当前订单的 paymentIndent
                                    var result = await HttpManager.getInstance().get("/sell/create-payment-intent?order_id=${context.read<SaleOrderProvider>().createSaleOrder.id}", cache: 0);
                                    print(result);
                                    if (result['code'] != 0) {
                                      return;
                                    }

                                    showModalBottomSheet(
                                      context: context,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                                        side: BorderSide.none,
                                      ),
                                      constraints: BoxConstraints(
                                        maxWidth: MediaQuery.of(context).size.width, // 设置最大宽度
                                      ),
                                      builder: (BuildContext context) {
                                        return Container(
                                          width: MediaQuery.of(context).size.width,
                                          padding: const EdgeInsets.all(0),
                                          child: PaymentPage(paymentIntentId: result['data']),
                                        );
                                      },
                                    );
                                  },
                                  style: ButtonStyle(backgroundColor: WidgetStateProperty.all(const Color(0xffcc003f))),
                                  child: const Text("Payment", style: TextStyle(color: Color(0xffffffff))),
                                )
                              : SizedBox(),
                          ElevatedButton(
                            onPressed: () async {
                              await context.read<SaleOrderProvider>().searchGoods("DS10");
                            },
                            child: const Text("+"),
                          ),
                        ],
                      ),
              ],
            ),
          ),
          const Align(
            alignment: Alignment.centerLeft,
            child: Padding(padding: EdgeInsets.fromLTRB(10, 0, 0, 0), child: Text("Product List", style: TextStyle())),
          ),
          Column(
            //children: context.watch<SaleOrderProvider>().orderGoodsList.map((e) => createOrderGoodsItem(e)).toList(),
            children: context.watch<SaleOrderProvider>().createOrderGoodsList.map((e) {
              return createOrderGoodsItem(e);
            }).toList(),
          ),
        ],
      ),
    );
  }
}

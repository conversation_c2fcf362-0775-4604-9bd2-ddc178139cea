import 'package:common_utils/common_utils.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/router/sale/createSale.dart';

import '../../model/SaleOrder.dart';
import '../../providers/saleorder_provider.dart';
import '../../utils/util.dart';
import '../statis/statis.dart';

class SaleOrder extends StatefulWidget {
  const SaleOrder({
    Key? key,
  }) : super(key: key);

  @override
  _SaleOrderList createState() => _SaleOrderList();
}

class _SaleOrderList extends State<StatefulWidget> {
  bool loading = false;
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  void _onRefresh() async {
    bool loadStatus = await context.read<SaleOrderProvider>().loadList();
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<SaleOrderProvider>().fetchNextList();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<SaleOrderProvider>().loadList();
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    _refreshController.dispose();
  }

  Widget createOrderItem(Saleorder _order) {
    return Container(
      margin: const EdgeInsets.fromLTRB(10, 5, 10, 5),
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.0)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _order.customer_name,
                    style: const TextStyle(),
                  ),
                  Utils.orderStatus(_order.order_status, _order.status_name),
                ],
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _order.order_no,
                    style: const TextStyle(
                      fontSize: 15,
                      color: Color(0xffcc003f),
                    ),
                  ),
                  Text(
                    Utils.transTime(_order.createTime),
                    style: const TextStyle(
                      color: Colors.black45,
                    ),
                  )
                ],
              ),
            ],
          ),
          DottedBorder(
            customPath: (size) {
              return Path()
                ..moveTo(0, 10)
                ..lineTo(size.width, 10);
            },
            dashPattern: const [8, 2],
            color: _order.order_status == 1 ? Colors.green : Colors.black26,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Order Summary",
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xff003473),
                    ),
                  ),
                  Text(
                    "Goods Amount : \$${_order.goods_amount}",
                    style: const TextStyle(color: Colors.black45, height: 1.6),
                  ),
                  Text(
                    "Tax Amount : \$${_order.tax_amount}",
                    style: const TextStyle(color: Colors.black45, height: 1.6),
                  ),
                  Text(
                    "Discount Amount : \$${_order.discount_amount}",
                    style: const TextStyle(color: Colors.black45, height: 1.6),
                  ),
                  Text(
                    "Order Amount : \$${_order.order_amount}",
                    style: const TextStyle(
                      color: Colors.black45,
                      height: 1.6,
                    ),
                  ),
                ],
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    "Payment Method",
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xff003473),
                    ),
                  ),
                  Text(
                    "Cash: \$${_order.cash}",
                    style: const TextStyle(
                      color: Colors.black45,
                      height: 1.6,
                    ),
                  ),
                  Text(
                    "Pos: \$${_order.pos}",
                    style: const TextStyle(
                      color: Colors.black45,
                      height: 1.6,
                    ),
                  ),
                  ElevatedButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(Color(0xff003473)),
                    ),
                    child: const Text("View"),
                    onPressed: () {
                      //set createSaleOrder & CreateGoodsList
                      context.read<SaleOrderProvider>().setCreateSaleOrder(_order.id);
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const CreateSale(),
                      ));
                    },
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  final _searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent,
                    width: 0.0,
                  ),
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.all(Radius.circular(7.0)),
                ),
                alignment: Alignment.center,
                height: 34,
                child: TextFormField(
                  controller: _searchController,
                  style: const TextStyle(fontSize: 16.0),
                  decoration: InputDecoration(
                    hintText: "Order No.",
                    contentPadding: const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    fillColor: Colors.transparent,
                    filled: true,
                    border: InputBorder.none,
                    prefixIcon: const Icon(Icons.search, color: Colors.black54, size: 21),
                    suffixIcon: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: const Icon(Icons.delete_outlined, color: Colors.black26, size: 21),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          context.read<SaleOrderProvider>().searchOrderNo = "";
                          context.read<SaleOrderProvider>().loadList();
                        });
                      },
                    ),
                    focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 0)),
                  ),
                  keyboardType: TextInputType.text,
                  autofocus: false,
                ),
              ),
            ),
            TextButton(
                onPressed: () {
                  context.read<SaleOrderProvider>().searchOrderNo = _searchController.text;
                  context.read<SaleOrderProvider>().loadList();
                },
                child: const Text("Search", style: TextStyle(color: Color(0xff003473))))
          ],
        ),
        actions: [
          TextButton(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(builder: (context) => const Statis()));
              },
              child: Text(
                "Statis",
                style: TextStyle(color: Color(0xff003473)),
              ))
        ],
      ),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext _context, LoadStatus? mode) {
            Widget body;
            LogUtil.e("data = ${mode}");
            if (mode == LoadStatus.idle) {
              body = const Text("Load More");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release Now");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("No More Data");
            } else {
              body = const Text("No More Data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView(
          children: context.watch<SaleOrderProvider>().orderList.map((e) => createOrderItem(e)).toList(),
        ),
      ),
    );
  }
}

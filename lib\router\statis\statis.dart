// var results = await showCalendarDatePicker2Dialog(
//   context: context,
//   config: CalendarDatePicker2WithActionButtonsConfig(),
//   dialogSize: const Size(325, 400),
//   initialValue: _dialogCalendarPickerValue,
//   borderRadius: BorderRadius.circular(15),
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:date_format/date_format.dart';
// );
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../main.dart';
import '../../service/HttpManager.dart';

class Statis extends StatefulWidget {
  const Statis({Key? key}) : super(key: key);
  @override
  _Statis createState() => _Statis();
}

List<DateTime> lastMonthRange = <DateTime>[];
List<DateTime> currentMonthRange = <DateTime>[];
List<DateTime> lastWeekRange = <DateTime>[];
List<DateTime> currentWeekRange = <DateTime>[];
List<DateTime> presentRange = <DateTime>[];

String orderCount = "0";
String priceAmount = "0";
String profitAmount = "0";
String costAmount = "0";

class _Statis extends State<Statis> {
  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    //上月
    lastMonthRange = presentRange = [DateTime(now.year, now.month - 1, 1), DateTime(now.year, now.month, 0)];
    //本月
    currentMonthRange = [DateTime(now.year, now.month, 1), DateTime(now.year, now.month, now.day)];
    //上周
    lastWeekRange = [DateTime(now.year, now.month, now.day - now.weekday - 7), DateTime(now.year, now.month, now.day - now.weekday + 1)];
    //本周start
    currentWeekRange = [DateTime(now.year, now.month, now.day - now.weekday), DateTime(now.year, now.month, now.day + 1, 0, 0, 0)];
  }

  @override
  void dispose() {
    super.dispose();
  }

  List<DateTime?>? _dialogCalendarPickerValue = <DateTime>[DateTime.now().add(const Duration(days: -30)), DateTime.now()];

  String choiseType = "0";
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xfff7f9fb),
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: const Text(
          "Statis",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Color(0xff003473),
          ),
        ),
      ),
      body: ListView(
        children: [
          Container(
            padding: EdgeInsets.all(10),
            margin: EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
            ),
            child: Column(
              children: [
                RadioListTile(
                  value: "0",
                  groupValue: choiseType,
                  onChanged: (Object? value) {
                    choiseType = value.toString();
                    presentRange = lastMonthRange;
                    setState(() {});
                  },
                  title: const Text("Last Month"),
                  subtitle: Text(formatDate(lastMonthRange[0], [m, '/', d, '/', yyyy]) + " - " + formatDate(lastMonthRange[1], [m, '/', d, '/', yyyy])),
                ),
                const Divider(height: 2.0, color: Colors.grey, indent: 70),
                RadioListTile(
                  value: "1",
                  groupValue: choiseType,
                  onChanged: (Object? value) {
                    choiseType = value.toString();
                    presentRange = currentMonthRange;
                    setState(() {});
                  },
                  title: const Text("Present Month"),
                  subtitle: Text(formatDate(currentMonthRange[0], [m, '/', d, '/', yyyy]) + " - " + formatDate(currentMonthRange[1], [m, '/', d, '/', yyyy])),
                ),
                const Divider(height: 2.0, color: Colors.grey, indent: 70),
                RadioListTile(
                  value: "2",
                  groupValue: choiseType,
                  onChanged: (Object? value) {
                    choiseType = value.toString();
                    presentRange = lastWeekRange;
                    setState(() {});
                  },
                  title: const Text("Last Week"),
                  subtitle: Text(formatDate(lastWeekRange[0], [m, '/', d, '/', yyyy]) + " - " + formatDate(lastWeekRange[1], [m, '/', d, '/', yyyy])),
                ),
                const Divider(height: 2.0, color: Colors.grey, indent: 70),
                RadioListTile(
                  value: "3",
                  groupValue: choiseType,
                  onChanged: (Object? value) {
                    choiseType = value.toString();
                    presentRange = currentWeekRange;
                    setState(() {});
                  },
                  title: const Text("Present Week"),
                  subtitle: Text(formatDate(currentWeekRange[0], [m, '/', d, '/', yyyy]) + " - " + formatDate(currentWeekRange[1], [m, '/', d, '/', yyyy])),
                ),
                const Divider(height: 2.0, color: Colors.grey, indent: 70),
                RadioListTile(
                  value: "4",
                  groupValue: choiseType,
                  onChanged: (Object? value) async {
                    choiseType = value.toString();
                    presentRange = currentWeekRange;
                    setState(() {});
                  },
                  title: const Text("Customize Date Range"),
                ),
                const Divider(height: 2.0, color: Colors.grey, indent: 70),
                choiseType == "4"
                    ? ListTile(
                        leading: const SizedBox(width: 35),
                        title: Text(formatDate(_dialogCalendarPickerValue![0]!, [m, '/', d, '/', yyyy]) + " - " + formatDate(_dialogCalendarPickerValue![1]!, [m, '/', d, '/', yyyy])),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () async {
                          final values = await showCalendarDatePicker2Dialog(
                            context: context,
                            config: CalendarDatePicker2WithActionButtonsConfig(
                              calendarType: CalendarDatePicker2Type.range,
                            ),
                            dialogSize: const Size(325, 450),
                            initialValue: _dialogCalendarPickerValue!,
                          );
                          if (values != null) {
                            setState(() {
                              _dialogCalendarPickerValue = values;
                              presentRange = [];
                              presentRange.add(_dialogCalendarPickerValue![0]!);
                              presentRange.add(_dialogCalendarPickerValue![1]!);
                            });
                          }
                        },
                      )
                    : const SizedBox(),
                Column(
                  children: [
                    ElevatedButton(
                      child: const Text("Search"),
                      onPressed: () async {
                        await HttpManager.getInstance()
                            .get(
                          "/statistice/app-statis?startTime=" + presentRange[0].toString() + "&endTime=" + presentRange[1].toString() + "&store_id=" + App.adminInfo.store_id.toString(),
                          cache: 0,
                        )
                            .then((e) {
                          if (e["code"] != 0) {
                          } else {
                            orderCount = e["data"]['orderCount'] ?? "0";
                            priceAmount = e["data"]['priceAmount'] ?? "0";
                            profitAmount = e["data"]['profitAmount'] ?? "0";
                            costAmount = e["data"]['costAmount'] ?? "0";
                            setState(() {});
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xff003473),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(10),
            margin: EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(10.0)),
            ),
            child: Column(
              children: [
                ListTile(
                  title: const Text("Order Count"),
                  trailing: Text(orderCount),
                ),
                ListTile(
                  title: const Text("Price Amount"),
                  trailing: Text(priceAmount),
                ),
                ListTile(
                  title: const Text("Profit Amount"),
                  trailing: Text(profitAmount),
                ),
                ListTile(
                  title: const Text("Cost Count"),
                  trailing: Text(costAmount),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
// SizedBox(
// height: 40,
// child: TextField(
// controller: _customerController,
// decoration: InputDecoration(
// fillColor: const Color(0xfff1f1f1),
// contentPadding: const EdgeInsets.fromLTRB(10.0, 6.0, 10.0, 0.0),
// filled: true,
// border: InputBorder.none,
// suffixIcon: IconButton(
// splashColor: Colors.transparent,
// highlightColor: Colors.transparent,
// icon: const Icon(Icons.clear, color: Colors.black26, size: 21),
// onPressed: () {
// _customerController.clear();
// },
// ),
// focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 0)),
// ),
// keyboardType: TextInputType.text,
// autofocus: false,
// ),
// ),

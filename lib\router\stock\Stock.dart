import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/model/StockGoods.dart';

import '../../main.dart';
import '../../providers/stock_provider.dart';

class Stock extends StatefulWidget {
  const Stock({Key? key}) : super(key: key);

  @override
  _Stock createState() => _Stock();
}

class _Stock extends State<StatefulWidget> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final _goodsNameController = TextEditingController();
  final _skuController = TextEditingController();
  final _codeController = TextEditingController();
  int _storeId = 0;

  void _onRefresh() async {
    bool loadStatus = await context.read<StockProvider>().loadList(_storeId);
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<StockProvider>().fetchNextList(_storeId);
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    _goodsNameController.text = context.read<StockProvider>().searchGoodsName;
    _skuController.text = context.read<StockProvider>().searchSku;
    _codeController.text = context.read<StockProvider>().searchCode;
    _storeId = App.adminInfo.store_id;
    context.read<StockProvider>().loadList(App.adminInfo.store_id);
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  Widget createItem(StockGoods _goods) {
    // 根据库存数量确定状态颜色
    int stockNum = int.tryParse(_goods.stock_num?.toString() ?? "0") ?? 0;
    Color statusColor;
    String statusText;
    
    if (stockNum > 50) {
      statusColor = const Color(0xff27AE60);
      statusText = "充足";
    } else if (stockNum > 10) {
      statusColor = const Color(0xffF39C12);
      statusText = "正常";
    } else if (stockNum > 0) {
      statusColor = const Color(0xffE74C3C);
      statusText = "偏低";
    } else {
      statusColor = const Color(0xff8E44AD);
      statusText = "缺货";
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部：商品名称和状态
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                // 状态指示器
                Container(
                  width: 4,
                  height: 24,
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                // 商品名称
                Expanded(
                  child: Text(
                    _goods.goods_name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xff2C3E50),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 状态标签
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 中间：详细信息
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // SKU和Code信息
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        "SKU",
                        _goods.sku ?? "--",
                        Icons.qr_code_2,
                        const Color(0xff3498DB),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoItem(
                        "Code",
                        _goods.code ?? "--",
                        Icons.tag,
                        const Color(0xff9B59B6),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                 
                 // 属性信息
                 if (_goods.category_attr.isNotEmpty)
                   Container(
                     width: double.infinity,
                     padding: const EdgeInsets.all(12),
                     decoration: BoxDecoration(
                       color: const Color(0xffF8F9FA),
                       borderRadius: BorderRadius.circular(8),
                       border: Border.all(
                         color: Colors.grey.shade300,
                         width: 1,
                       ),
                     ),
                     child: Column(
                       crossAxisAlignment: CrossAxisAlignment.start,
                       children: [
                         const Text(
                           "商品属性",
                           style: TextStyle(
                             fontSize: 12,
                             fontWeight: FontWeight.w600,
                             color: Color(0xff7F8C8D),
                           ),
                         ),
                         const SizedBox(height: 8),
                         Wrap(
                           spacing: 8,
                           runSpacing: 6,
                           children: _goods.category_attr.take(6).map((e) {
                             final colors = [
                               const Color(0xff3498DB),
                               const Color(0xff9B59B6),
                               const Color(0xffE67E22),
                               const Color(0xff1ABC9C),
                               const Color(0xffE74C3C),
                               const Color(0xff34495E),
                             ];
                             final colorIndex = _goods.category_attr.indexOf(e) % colors.length;
                             return Container(
                               padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                               decoration: BoxDecoration(
                                 color: colors[colorIndex].withOpacity(0.1),
                                 borderRadius: BorderRadius.circular(12),
                                 border: Border.all(
                                   color: colors[colorIndex].withOpacity(0.3),
                                   width: 1,
                                 ),
                               ),
                               child: RichText(
                                 text: TextSpan(
                                   children: [
                                     TextSpan(
                                       text: "${e.name?.toString() ?? ""}",
                                       style: TextStyle(
                                         fontSize: 11,
                                         fontWeight: FontWeight.w600,
                                         color: colors[colorIndex],
                                       ),
                                     ),
                                     const TextSpan(
                                       text: ": ",
                                       style: TextStyle(
                                         fontSize: 11,
                                         color: Color(0xff7F8C8D),
                                       ),
                                     ),
                                     TextSpan(
                                       text: "${e.value?.toString() ?? ""}",
                                       style: const TextStyle(
                                         fontSize: 11,
                                         fontWeight: FontWeight.w500,
                                         color: Color(0xff2C3E50),
                                       ),
                                     ),
                                   ],
                                 ),
                               ),
                             );
                           }).toList(),
                         ),
                       ],
                     ),
                   ),
                 
                 if (_goods.category_attr.isNotEmpty)
                   const SizedBox(height: 16),
                 
                 // 库存数量
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "当前库存",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff7F8C8D),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.inventory_2,
                            size: 16,
                            color: statusColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            stockNum.toString(),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: statusColor,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Text(
                            "件",
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: statusColor.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: color.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xff2C3E50),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: const Text(
          "Stock Status",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Color(0xff003473),
          ),
        ),
        actions: [
          IconButton(
              onPressed: () {
                _scaffoldKey.currentState!.openEndDrawer();
              },
              icon: const Icon(Icons.tune))
        ],
      ),
      endDrawer: Drawer(
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SafeArea(
                child: Center(child: Text("Search Condition", style: TextStyle(fontSize: 20))),
              ),
              const Text(
                "Goods Name",
                style: TextStyle(height: 2),
              ),
              Container(
                height: 40,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent,
                    width: 0.0,
                  ),
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.all(Radius.circular(7.0)),
                ),
                child: TextField(
                  controller: _goodsNameController,
                  style: const TextStyle(fontSize: 16.0),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    filled: false,
                    border: InputBorder.none,
                    suffixIcon: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: const Icon(Icons.clear, color: Colors.black26, size: 21),
                      onPressed: () {
                        _goodsNameController.clear();
                        context.read<StockProvider>().searchGoodsName = "";
                      },
                    ),
                    focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 0)),
                  ),
                  keyboardType: TextInputType.text,
                  autofocus: false,
                ),
              ),
              const Text(
                "Sku",
                style: TextStyle(height: 2),
              ),
              Container(
                height: 40,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent,
                    width: 0.0,
                  ),
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.all(Radius.circular(7.0)),
                ),
                child: TextField(
                  controller: _skuController,
                  style: const TextStyle(fontSize: 16.0, height: 1.5),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    filled: false,
                    border: InputBorder.none,
                    suffixIcon: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: const Icon(Icons.clear, color: Colors.black26, size: 21),
                      onPressed: () {
                        _skuController.clear();
                        context.read<StockProvider>().searchSku = "";
                      },
                    ),
                    focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 0)),
                  ),
                  keyboardType: TextInputType.text,
                  autofocus: false,
                ),
              ),
              const Text(
                "Code",
                style: TextStyle(height: 2),
              ),
              Container(
                height: 40,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent,
                    width: 0.0,
                  ),
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.all(Radius.circular(7.0)),
                ),
                child: TextField(
                  controller: _codeController,
                  style: const TextStyle(fontSize: 16.0),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                    filled: false,
                    border: InputBorder.none,
                    suffixIcon: IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      icon: const Icon(Icons.clear, color: Colors.black26, size: 21),
                      onPressed: () {
                        _codeController.clear();
                        context.read<StockProvider>().searchCode = "";
                      },
                    ),
                    focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 0)),
                  ),
                  keyboardType: TextInputType.text,
                  autofocus: false,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  CupertinoButton(
                      child: const Text("Search"),
                      onPressed: () {
                        context.read<StockProvider>().searchGoodsName = _goodsNameController.text;
                        context.read<StockProvider>().searchSku = _skuController.text;
                        context.read<StockProvider>().searchCode = _codeController.text;

                        context.read<StockProvider>().loadList(_storeId);
                        _scaffoldKey.currentState!.closeEndDrawer();
                      }),
                  CupertinoButton(
                      child: const Text(
                        "Reset",
                        style: TextStyle(fontSize: 15, color: Colors.black26),
                      ),
                      onPressed: () {
                        _skuController.clear();
                        context.read<StockProvider>().searchSku = "";
                        _goodsNameController.clear();
                        context.read<StockProvider>().searchGoodsName = "";
                        _codeController.clear();
                        context.read<StockProvider>().searchCode = "";
                        context.read<StockProvider>().loadList(_storeId);
                        _scaffoldKey.currentState!.closeEndDrawer();
                      }),
                ],
              )
            ],
          ),
        ),
      ),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext _context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.idle) {
              body = const Text("Load More");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release Now");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("No More Data");
            } else {
              body = const Text("No More Data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView(
          children: context.watch<StockProvider>().goodsList.map((e) => createItem(e as StockGoods)).toList(),
        ),
      ),
    );
  }
}

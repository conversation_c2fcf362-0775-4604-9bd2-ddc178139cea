import 'dart:async';
import 'dart:io';

import 'package:provider/provider.dart';
import 'package:tattooerp20260622/models/not_found_location_exeception.dart';
import 'package:tattooerp20260622/router/stripe_pay/readers.dart';
import 'package:tattooerp20260622/utils/linear_progress_indicator_bar.dart';
import 'package:tattooerp20260622/utils/permission_utils.dart';
import 'package:tattooerp20260622/utils/reader_delegates.dart';
import 'package:tattooerp20260622/utils/state_tools.dart';
import 'package:tattooerp20260622/utils/stripe_api.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tattooerp20260622/providers/stripepay_provider.dart';

class InitializationStripe extends StatefulWidget {
  const InitializationStripe({
    super.key,
  });

  @override
  State<InitializationStripe> createState() => _InitializationState();
}

class _InitializationState extends State<InitializationStripe> with StateTools {
  @override
  void initState() {
    // TODO: implement initState
    //检查是不是已经初始化 和 连接设备
    if (Terminal.isInitialized) {
      _fetchLocations();
      setState(() {
        _currentStep = 1;
      });
    }

    if (context.read<StripePayProvider>().reader != null) {
      setState(() {
        _currentStep = 2;
      });
    }

    super.initState();
  }

  //初始化TerminalStripe
  Future<void> _initTerminal() async {
    //检查如果没有初始化就初始化
    if (!Terminal.isInitialized) {
      final permissions = [Permission.locationWhenInUse, Permission.bluetooth, Permission.bluetoothScan, Permission.bluetoothConnect];
      for (final permission in permissions) {
        final status = await permission.request();
        report('$permission: $status');
        if (status == PermissionStatus.denied || status == PermissionStatus.permanentlyDenied) {
          showSnackBar('Please grant ${permission.name} permission.');
          return;
        }
      }
      if (kReleaseMode) {
        for (final service in permissions.whereType<PermissionWithService>()) {
          final status = await service.serviceStatus;
          report('$service: $status');

          if (status != PermissionStatus.granted) {
            showSnackBar('Please enable ${service.name} service.');
            return;
          }
        }
      }

      await Terminal.initTerminal(
        shouldPrintLogs: true,
        fetchToken: StripeApi.instance.createTerminalConnectionToken,
      );
    }
  }

  var _locations = <Location>[];

  Future<void> _fetchLocations() async {
    setState(() => _locations = const []);
    final locations = await Terminal.instance.listLocations();
    setState(() => _locations = locations);
  }

  int _currentStep = 0;

  void _toggleLocation(Location location) {
    context.read<StripePayProvider>().setLocation(location);
  }

  var _readers = const <Reader>[];
  var _isSimulated = true;
  StreamSubscription<List<Reader>>? _discoverReaderSub;

  void _startDiscoverReaders() async {
    setState(() => _readers = const []);

    final configuration = BluetoothProximityDiscoveryConfiguration(
      isSimulated: _isSimulated,
    );

    final discoverReaderStream = Terminal.instance.discoverReaders(configuration);

    setState(() {
      _discoverReaderSub = discoverReaderStream.listen((readers) {
        setState(() => _readers = readers);
      }, onDone: () {
        setState(() => _discoverReaderSub = null);
      });
    });
  }

  Future<void> _stopDiscoverReaders() async {
    await _discoverReaderSub?.cancel();
    setState(() => _discoverReaderSub = null);
  }

  Future<void> _connectReader(Reader reader) async {
    try {
      final connectionConfiguration = BluetoothConnectionConfiguration(
        locationId: context.read<StripePayProvider>().selectedLocation!.id!,
        readerDelegate: LoggingMobileReaderDelegate(showSnackBar),
      );

      final connectedReader = await Terminal.instance.connectReader(reader, configuration: connectionConfiguration);

      showSnackBar('Connected to a device: ${connectedReader.label ?? connectedReader.serialNumber}');
      context.read<StripePayProvider>().setReader(connectedReader);
    } on NotFoundLocationException {
      showSnackBar('Location not selected!');
    }
  }

  Future<void> _disconnectReader() async {
    final reader = context.read<StripePayProvider>().reader;
    await Terminal.instance.disconnectReader();
    showSnackBar('Terminal ${reader?.label ?? reader?.serialNumber ?? '???'} disconnected');
    context.read<StripePayProvider>().setReader(null);
  }

  @override
  Widget build(BuildContext context) {
    Location? presentLocation = context.watch<StripePayProvider>().selectedLocation;

    return Scaffold(
      appBar: AppBar(
        title: Text('Initialization Stripe'),
        bottom: isMutating ? const LinearProgressIndicatorBar() : null,
      ),
      body: Stepper(
        type: StepperType.horizontal,
        currentStep: _currentStep,
        controlsBuilder: (BuildContext context, ControlsDetails details) {
          // 返回空容器以隐藏默认按钮
          return Container();
        },
        steps: [
          Step(
            title: Text("Initialization"),
            content: Container(
              alignment: Alignment.center,
              child: ElevatedButton(
                onPressed: () async {
                  mutate(_initTerminal);
                  setState(() {
                    _currentStep = 1;
                  });
                  _fetchLocations();
                },
                child: Text('Initialization Stripe'),
              ),
            ),
            isActive: _currentStep >= 0,
            state: _currentStep > 0 ? StepState.complete : StepState.indexed,
          ),
          Step(
            title: Text("Location"),
            content: Column(
              children: [
                FilledButton.tonal(
                  onPressed: !isMutating ? () => mutate(_fetchLocations) : null,
                  child: const Text('Fetch Locations'),
                ),
                const Divider(height: 32.0),
                ..._locations.map((e) {
                  return ListTile(
                    selected: context.watch<StripePayProvider>().selectedLocation?.id == e.id,
                    onTap: () => _toggleLocation(e),
                    dense: true,
                    title: Text('${e.id}: ${e.displayName}'),
                    subtitle: Text('${e.address?.city},${e.address?.state},${e.address?.line1}'),
                  );
                }),
                const Divider(height: 32.0),
                context.watch<StripePayProvider>().selectedLocation == null
                    ? Container()
                    : Column(
                        children: [
                          Text("${presentLocation?.id}: ${presentLocation?.displayName}"),
                          Text('${presentLocation?.address?.city},${presentLocation?.address?.state},${presentLocation?.address?.line1}'),
                          FilledButton.tonal(
                            onPressed: () {
                              setState(() {
                                _currentStep = 2;
                              });
                            },
                            child: const Text('Next'),
                          )
                        ],
                      )
              ],
            ),
            isActive: _currentStep >= 1,
            state: _currentStep > 1 ? StepState.complete : StepState.indexed,
          ),
          Step(
            title: Text("Connect"),
            content: Readers(),
            isActive: _currentStep > 2,
            state: _currentStep >= 2 ? StepState.complete : StepState.indexed,
          )
        ],
      ),
    );
  }
}

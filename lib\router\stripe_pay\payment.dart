import 'dart:convert';

import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/models/k.dart';
import 'package:tattooerp20260622/providers/stripepay_provider.dart';
import 'package:tattooerp20260622/router/stripe_pay/init.dart';
import 'package:tattooerp20260622/utils/linear_progress_indicator_bar.dart';
import 'package:tattooerp20260622/utils/state_tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';

class PaymentPage extends StatefulWidget {
  final String paymentIntentId;

  const PaymentPage({super.key, required this.paymentIntentId});

  @override
  State<PaymentPage> createState() => _PaymentPage();
}

class _PaymentPage extends State<PaymentPage> with StateTools {
  PaymentIntent? _paymentIntent;
  CancelableFuture<PaymentIntent>? _collectingPaymentMethod;

  Future<void> _collectPaymentMethod() async {
    final paymentIntent = await Terminal.instance.retrievePaymentIntent(widget.paymentIntentId);
    final collectingPaymentMethod = Terminal.instance.collectPaymentMethod(
      paymentIntent,
      skipTipping: true,
    );
    setState(() {
      _collectingPaymentMethod = collectingPaymentMethod;
    });

    try {
      final paymentIntentWithPaymentMethod = await collectingPaymentMethod;
      setState(() {
        _paymentIntent = paymentIntentWithPaymentMethod;
        _collectingPaymentMethod = null;
      });
      showSnackBar('Payment method collected!');
    } on TerminalException catch (exception) {
      setState(() => _collectingPaymentMethod = null);
      switch (exception.code) {
        case TerminalExceptionCode.canceled:
          showSnackBar('Collecting Payment method is cancelled!');
        default:
          rethrow;
      }
    }
  }

  Future<void> _cancelCollectingPaymentMethod(CancelableFuture<PaymentIntent> cancelable) async {
    await cancelable.cancel();
  }

  Future<void> _confirmPaymentIntent(PaymentIntent paymentIntent) async {
    final processedPaymentIntent = await Terminal.instance.confirmPaymentIntent(paymentIntent);
    setState(() => _paymentIntent = processedPaymentIntent);
    showSnackBar('Payment processed!');
  }

  @override
  Widget build(BuildContext context) {
    final paymentStatusNotifier = ValueNotifier<PaymentStatus>(PaymentStatus.notReady);
    final paymentStatus = watch(paymentStatusNotifier);
    final paymentIntent = _paymentIntent;
    final collectingPaymentMethod = _collectingPaymentMethod;

    return SingleChildScrollView(
      child: Column(
        children: [
          ListTile(
            selected: true,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Payment By Stripe'),
                TextButton(
                    onPressed: () {
                      Navigator.of(context).push(MaterialPageRoute(builder: (context) => InitializationStripe()));
                    },
                    child: Text("setting"))
              ],
            ),
          ),
          const Divider(height: 32.0),
          const SizedBox(height: 22.0),
          if (collectingPaymentMethod == null)
            FilledButton(
              onPressed: !isMutating ? () => mutate(() async => _collectPaymentMethod()) : null,
              child: const Text('Collect Payment Method'),
            )
          else
            FilledButton(
              onPressed: () async => _cancelCollectingPaymentMethod(collectingPaymentMethod),
              child: const Text('Cancel Collecting Payment Method'),
            ),
          const SizedBox(height: 22.0),
          FilledButton(
            onPressed: !isMutating && paymentIntent != null && paymentIntent.status == PaymentIntentStatus.requiresConfirmation ? () => mutate(() async => _confirmPaymentIntent(paymentIntent)) : null,
            child: const Text('Confirm PaymentIntent'),
          ),
          SizedBox(height: 50)
        ],
      ),
    );
  }
}

import 'dart:async';

import 'package:provider/provider.dart';
import 'package:tattooerp20260622/models/discovery_method.dart';
import 'package:tattooerp20260622/models/k.dart';
import 'package:tattooerp20260622/models/not_found_location_exeception.dart';
import 'package:tattooerp20260622/providers/stripepay_provider.dart';
import 'package:tattooerp20260622/utils/linear_progress_indicator_bar.dart';
import 'package:tattooerp20260622/utils/reader_delegates.dart';
import 'package:tattooerp20260622/utils/state_tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mek_stripe_terminal/mek_stripe_terminal.dart';

import '../../main.dart';

class Readers extends StatefulWidget {
  const Readers({
    super.key,
  });

  @override
  State<Readers> createState() => _ReadersState();
}

class _ReadersState extends State<Readers> with StateTools {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  var _isSimulated = true;
  final _discoveryMethod = DiscoveryMethod.bluetoothScan;

  StreamSubscription<List<Reader>>? _discoverReaderSub;
  var _readers = const <Reader>[];

  Future<void> _checkStatus() async {
    final status = await Terminal.instance.getConnectionStatus();
    showSnackBar('Connection status: ${status.name}');
  }

  // Future<void> _changeDiscoveryMethod(DiscoveryMethod? method) async {
  //   setState(() {
  //     _discoveryMethod = method!;
  //     _readers = const [];
  //   });
  //   await _stopDiscoverReaders();
  // }

  Future<void> _changeMode() async {
    setState(() {
      _isSimulated = !_isSimulated;
      _readers = const [];
    });
    await _stopDiscoverReaders();
  }

  void _startDiscoverReaders() {
    setState(() => _readers = const []);
    final configuration = BluetoothDiscoveryConfiguration(
      isSimulated: _isSimulated,
    );
    final discoverReaderStream = Terminal.instance.discoverReaders(configuration);
    setState(() {
      _discoverReaderSub = discoverReaderStream.listen((readers) {
        setState(() => _readers = readers);
      }, onDone: () {
        setState(() => _discoverReaderSub = null);
      });
    });
  }

  Future<void> _stopDiscoverReaders() async {
    await _discoverReaderSub?.cancel();
    setState(() => _discoverReaderSub = null);
  }

  Future<void> _connectReader(Reader reader) async {
    final stripePayProvider = context.read<StripePayProvider>();
    String getLocationId() {
      final locationId = context.read<StripePayProvider>().selectedLocation?.id ?? reader.locationId;
      if (locationId != null) return locationId;
      throw NotFoundLocationException();
    }

    try {
      final connectionConfiguration = BluetoothConnectionConfiguration(locationId: getLocationId(), readerDelegate: LoggingMobileReaderDelegate(showSnackBar));
      final connectedReader = await Terminal.instance.connectReader(reader, configuration: connectionConfiguration);
      stripePayProvider.setReader(connectedReader);
      showSnackBar('Connected to a device: ${connectedReader.label ?? connectedReader.serialNumber}');
      // widget.connectedReaderNotifier.value = connectedReader;
      App.reader = connectedReader;
      if (!mounted) return;
    } on NotFoundLocationException {
      showSnackBar('Location not selected!');
    }
  }

  Future<void> _disconnectReader() async {
    final reader = context.read<StripePayProvider>().reader;
    await Terminal.instance.disconnectReader();
    showSnackBar('Terminal ${reader?.label ?? reader?.serialNumber ?? '???'} disconnected');
  }

  @override
  Widget build(BuildContext context) {
    // final connectionStatus = watch(widget.connectionStatusListenable);
    // final connectedReader = watch(widget.connectedReaderNotifier);

    return SingleChildScrollView(
      child: Column(
        children: [
          FilledButton.tonal(
            onPressed: !isMutating ? () => mutate(_checkStatus) : null,
            child: Text('Check status (${context.watch<StripePayProvider>().connectionStatus.name})'),
          ),
          const Divider(height: 32.0),
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          //   child: DropdownButtonFormField<DiscoveryMethod>(
          //     value: _discoveryMethod,
          //     onChanged: !isMutating && connectionStatus == ConnectionStatus.notConnected ? _changeDiscoveryMethod : null,
          //     isExpanded: true,
          //     decoration: const InputDecoration(labelText: 'Discovery method'),
          //     items: DiscoveryMethod.values.map((e) {
          //       return DropdownMenuItem(value: e, child: Text(e.name));
          //     }).toList(),
          //   ),
          // ),
          if (_discoveryMethod.canSimulate) SwitchListTile(onChanged: !isMutating && context.read<StripePayProvider>().connectionStatus == ConnectionStatus.notConnected ? (_) => mutate(_changeMode) : null, value: _isSimulated, title: const Text('Is simulate scanning mode?')),
          const SizedBox(height: 12.0),
          if (context.read<StripePayProvider>().connectionStatus == ConnectionStatus.connected)
            FilledButton(
              onPressed: !isMutating ? () => mutate(_disconnectReader) : null,
              child: const Text('Disconnect Reader'),
            )
          else if (_discoverReaderSub == null)
            FilledButton(
              onPressed: !isMutating && context.read<StripePayProvider>().connectionStatus == ConnectionStatus.notConnected ? _startDiscoverReaders : null,
              child: const Text('Scan Devices'),
            )
          else
            FilledButton(
              onPressed: !isMutating && context.read<StripePayProvider>().connectionStatus == ConnectionStatus.discovering ? _stopDiscoverReaders : null,
              child: const Text('Stop Scanning'),
            ),
          const Divider(height: 32.0),
          ..._readers.map((reader) {
            return ListTile(
              selected: reader.serialNumber == context.watch<StripePayProvider>().reader?.serialNumber,
              enabled: !isMutating && context.read<StripePayProvider>().connectionStatus != ConnectionStatus.connecting && (context.watch<StripePayProvider>().reader == null || context.watch<StripePayProvider>().reader?.serialNumber == reader.serialNumber),
              onTap: () => mutate(() async => _connectReader(reader!)),
              title: Text(reader.serialNumber),
              subtitle: Text('${reader.deviceType?.name ?? 'Unknown'} ${reader.locationId ?? 'NoLocation'}'),
              trailing: Text('${(reader.batteryLevel * 100).toInt()}'),
            );
          }),
          if (context.watch<StripePayProvider>().reader != null) ...[
            const SizedBox(height: 8.0),
            FilledButton.tonal(
              onPressed: !isMutating
                  ? () => mutate(() async => await Terminal.instance.setReaderDisplay(const Cart(
                        currency: K.currency,
                        tax: 130,
                        total: 1000,
                        lineItems: [
                          CartLineItem(
                            description: 'hello 1',
                            quantity: 1,
                            amount: 500,
                          ),
                          CartLineItem(
                            description: 'hello 2',
                            quantity: 1,
                            amount: 500,
                          ),
                        ],
                      )))
                  : null,
              child: const Text('Set reader display'),
            ),
          ],
        ],
      ),
    );
  }
}

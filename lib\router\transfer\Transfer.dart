import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tattooerp20260622/utils/util.dart';

import '../../model/TransferOrder.dart';
import '../../providers/transfer_provider.dart';
import 'TransferDetail.dart';

class Transfer extends StatefulWidget {
  const Transfer({
    Key? key,
  }) : super(key: key);

  @override
  _Transfer createState() => _Transfer();
}

class _Transfer extends State<StatefulWidget> {
  bool loading = false;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    bool loadStatus = await context.read<TransferProvider>().loadList();
    _refreshController.refreshCompleted();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  void _onLoading() async {
    bool loadStatus = await context.read<TransferProvider>().fetchNextList();
    if (loadStatus) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<TransferProvider>().loadList();
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
    _refreshController.dispose();
  }

  Widget createOrderItem(TransferOrder _order) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => TransferDetail(id: _order.id.toString()),
        ));
      },
      child: Container(
        color: Colors.white,
        margin: const EdgeInsets.fromLTRB(0, 5, 0, 5),
        padding: const EdgeInsets.all(10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _order.order_no,
                      style: const TextStyle(
                        fontSize: 15,
                        color: Color(0xffcc003f),
                      ),
                    ),
                    Text(
                      Utils.transTime(_order.createTime),
                      style: const TextStyle(
                        color: Colors.black45,
                      ),
                    )
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      _order.from_store_name,
                      style: const TextStyle(),
                    ),
                    Text(
                      _order.status_name,
                      style: const TextStyle(),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  final _searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: const Size(750, 1334), minTextAdapt: true);
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
          elevation: 3.0,
          iconTheme: const IconThemeData(color: Color(0xff003473)),
          backgroundColor: const Color(0xfff7f9fb),
          centerTitle: true,
          automaticallyImplyLeading: true,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.transparent,
                      width: 0.0,
                    ),
                    color: Colors.grey[200],
                    borderRadius: const BorderRadius.all(Radius.circular(7.0)),
                  ),
                  alignment: Alignment.center,
                  height: 34,
                  child: TextFormField(
                    controller: _searchController,
                    style: const TextStyle(fontSize: 16.0),
                    decoration: InputDecoration(
                      hintText: "Order No.",
                      contentPadding:
                          const EdgeInsets.fromLTRB(10.0, 0.0, 10.0, 0.0),
                      fillColor: Colors.transparent,
                      filled: true,
                      border: InputBorder.none,
                      prefixIcon: const Icon(Icons.search,
                          color: Colors.black54, size: 21),
                      suffixIcon: IconButton(
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        icon: const Icon(Icons.delete_outlined,
                            color: Colors.black26, size: 21),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            context.read<TransferProvider>().orderNo = "";
                            context.read<TransferProvider>().loadList();
                          });
                        },
                      ),
                      focusedBorder: const OutlineInputBorder(
                          borderSide:
                              BorderSide(color: Colors.transparent, width: 0)),
                    ),
                    keyboardType: TextInputType.text,
                    autofocus: false,
                  ),
                ),
              ),
              TextButton(
                  onPressed: () {
                    context.read<TransferProvider>().orderNo =
                        _searchController.text;
                    context.read<TransferProvider>().loadList();
                  },
                  child: const Text("Search"))
            ],
          )),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext _context, LoadStatus? mode) {
            Widget body;
            LogUtil.e("data = ${mode}");
            if (mode == LoadStatus.idle) {
              body = const Text("Load More");
            } else if (mode == LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("Release Now");
            } else if (mode == LoadStatus.noMore) {
              body = const Text("No More Data");
            } else {
              body = const Text("No More Data");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView(
          children: context
              .watch<TransferProvider>()
              .orderList
              .map((e) => createOrderItem(e))
              .toList(),
        ),
      ),
    );
  }
}

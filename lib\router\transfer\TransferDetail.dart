import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../model/TransferOrderGoods.dart';
import '../../providers/transfer_provider.dart';
import '../../utils/util.dart';

class TransferDetail extends StatefulWidget {
  const TransferDetail({
    super.key,
    this.id,
  });
  final String? id;
  @override
  State<StatefulWidget> createState() {
    return _TransferDetail();
  }
}

class _TransferDetail extends State<TransferDetail> {
  bool loading = false;

  @override
  void initState() {
    super.initState();
    context.read<TransferProvider>().getDetail(widget.id);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget createOrderGoodsItem(TransferOrderGoods _goods) {
    return Container(
        color: Colors.white,
        margin: const EdgeInsets.fromLTRB(0, 3, 0, 3),
        padding: const EdgeInsets.all(10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(_goods.goods_name,
                          style: const TextStyle(fontSize: 16)),
                      Text("Sku: ${_goods.sku ?? ""}",
                          style: const TextStyle()),
                      Text("Code: ${_goods.code ?? ""}",
                          style: const TextStyle())
                    ],
                  ),
                ),
                const SizedBox(width: 5),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _goods.category_attr.isNotEmpty
                        ? _goods.category_attr.map((e) {
                            return Text(
                                (e.name ?? "") + ": " + (e.value ?? ""));
                          }).toList()
                        : [],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _goods.in_num.toString(),
                        style: const TextStyle(
                          fontSize: 15,
                          color: Color(0xffcc003f),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff7f9fb),
      //backgroundColor: Colors.amberAccent,
      appBar: AppBar(
        elevation: 3.0,
        iconTheme: const IconThemeData(color: Color(0xff003473)),
        backgroundColor: const Color(0xfff7f9fb),
        centerTitle: true,
        automaticallyImplyLeading: true,
        title: Text(
          context.watch<TransferProvider>().transferOrder.order_no,
          style: const TextStyle(color: Colors.black),
        ),
      ),
      body: Column(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "from Store: ${context.watch<TransferProvider>().transferOrder.from_store_name}",
                          style: const TextStyle(height: 2),
                        ),
                        Text(
                          Utils.transTime(context
                              .watch<TransferProvider>()
                              .transferOrder
                              .createTime),
                          style:
                              const TextStyle(color: Colors.black45, height: 2),
                        )
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                          context
                              .watch<TransferProvider>()
                              .transferOrder
                              .status_name,
                          style: const TextStyle()),
                    ],
                  ),
                ],
              ),
            ],
          ),
          Expanded(
            child: ListView(
              children: context
                  .watch<TransferProvider>()
                  .orderGoodsList
                  .map((e) => createOrderGoodsItem(e))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}

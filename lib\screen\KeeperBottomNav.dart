import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../router/instock/Instock.dart';
import '../router/inventory/Inventory.dart';
import '../router/me/Me.dart';
import '../router/outstock/Outstock.dart';
import '../router/stock/Stock.dart';

class KeeperBottomMav extends StatefulWidget {
  const KeeperBottomMav({Key? key}) : super(key: key);

  @override
  _KeeperBottomMav createState() => _KeeperBottomMav();
}

class _KeeperBottomMav extends State<StatefulWidget> with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  List<Widget> keeperList = [
    const Instock(),
    const Outstock(),
    const Stock(),
    const Inventory(),
    const Me(),
  ];

  List<BottomNavigationBarItem> keeperNavList = const [
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/inStock.png")), label: "inStock"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/outstock.png")), label: "outStock"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/stock.png")), label: "Stock"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/inventory.png")), label: "Inventory"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/avatar.png")), label: "Me"),
  ];

  @override
  void dispose() {
    super.dispose();
  }

  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(750, 1334), minTextAdapt: true);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            backgroundColor: Colors.transparent,
            selectedItemColor: const Color(0xFF1976D2),
            unselectedItemColor: const Color(0xFF9E9E9E),
            type: BottomNavigationBarType.fixed,
            elevation: 0,
            selectedFontSize: 12.0,
            unselectedFontSize: 10.0,
            selectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
            ),
            items: keeperNavList,
            currentIndex: _selectedIndex,
            onTap: _onItemTapped,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      body: IndexedStack(
        index: _selectedIndex,
        children: keeperList,
      ),
    );
  }
}

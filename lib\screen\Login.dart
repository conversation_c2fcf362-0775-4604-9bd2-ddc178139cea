import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tattooerp20260622/service/HttpManager.dart';
import 'package:tattooerp20260622/utils/storage.dart';

import '../common/Config.dart';
import '../common/Screen.dart';
import '../main.dart';
import '../model/ResponseModel.dart';
import '../model/User.dart';

class Login extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return LoginPage();
  }
}

class LoginPage extends State<StatefulWidget> {
  bool pwdShow = false; //密码是否显示明文

  final nameController = TextEditingController();
  final pwdController = TextEditingController();

  @override
  void dispose() {
    nameController.dispose();
    pwdController.dispose();
    super.dispose();
  }

  @override
  initState() {
    super.initState();
    //检查用户token 如果有登录 就去首页
    LocalStorage.getString("user_token").then((e) async {
      if (e is String) {
        await LocalStorage.getString("user_info").then((e) {
          if (e == null || e.isBlank) {
            return;
          }
          App.adminInfo = Admin.fromJson(jsonDecode(e));
          if (App.adminInfo.store_id > 1) {
            Navigator.pushNamedAndRemoveUntil(
              context,
              "sellerNav",
              (Route<dynamic> route) => false,
            );
          } else {
            Navigator.pushNamedAndRemoveUntil(
              context,
              "keeperNav",
              (Route<dynamic> route) => false,
            );
          }
        });
      }
    });
    nameController.text = "admin";
    pwdController.text = "admin111";
  }

  login() async {
    var options = BaseOptions(
      baseUrl: Config.baseUrl,
      connectTimeout: const Duration(seconds: 50),
      receiveTimeout: const Duration(seconds: 30),
      headers: {'Content-type': 'application/json'},
    );
    Dio dio = Dio(options);
    try {
      Response response = await dio.post(
        '/admin/login',
        data: {"userName": nameController.text, "password": pwdController.text},
      );

      var responseModel = ResponseModel.fromJson(response.data);
      if (responseModel.code == 0) {
        Fluttertoast.showToast(
          msg: "Successfully",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
        LocalStorage.setString("user_token", responseModel.data);
        //获取用户基本信息
        await HttpManager.getInstance().get('/admin/get-present-user').then((
          e,
        ) {
          LocalStorage.setString("user_info", jsonEncode(e["data"]));
          App.adminInfo = Admin.fromJson(e["data"]);
          if (App.adminInfo.store_id > 1) {
            Navigator.pushNamedAndRemoveUntil(
              context,
              "sellerNav",
              (Route<dynamic> route) => false,
            );
          } else {
            Navigator.pushNamedAndRemoveUntil(
              context,
              "keeperNav",
              (Route<dynamic> route) => false,
            );
          }
        });
      } else {
        Fluttertoast.showToast(
          msg: responseModel.msg,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.white,
          fontSize: 15.0,
        );
      }
    } on DioError catch (e) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx and is also not 304.
      if (e.response != null) {
        print(e.response?.data);
        print(e.response?.headers);
        print(e.response?.requestOptions);
      } else {
        // Something happened in setting up or sending the request that triggered an Error
        print(e.requestOptions);
        print(e.message);
        Fluttertoast.showToast(
          msg: e.message ?? "",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black45,
          textColor: Colors.red,
          fontSize: 15.0,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Screen().init(context);
    double width = MediaQuery.of(context).size.width;
    return Material(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF1976D2), Color(0xFF1565C0), Color(0xFF0D47A1)],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: SizedBox(
                        width: width * .4,
                        child: Image.asset(
                          "images/logo.png",
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 40),
                    const Text(
                      "Welcome Back",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      "Sign in to continue",
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 40),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 8,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextField(
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                          controller: nameController,
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                            border: InputBorder.none,
                            hintText: "Admin Name",
                            hintStyle: TextStyle(color: Colors.grey),
                            prefixIcon: Icon(
                              Icons.person_outline,
                              color: Colors.grey,
                            ),
                          ),
                          autofocus: false,
                          onChanged: (val) {},
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 8,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: pwdController,
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                          obscureText: true,
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                            border: InputBorder.none,
                            hintText: "Password",
                            hintStyle: TextStyle(color: Colors.grey),
                            prefixIcon: Icon(
                              Icons.lock_outline,
                              color: Colors.grey,
                            ),
                          ),
                          autofocus: false,
                          onChanged: (val) {},
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Container(
                        width: double.infinity,
                        height: 56,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF4CAF50).withOpacity(0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              login();
                            },
                            borderRadius: BorderRadius.circular(16),
                            child: const Center(
                              child: Text(
                                "Login",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

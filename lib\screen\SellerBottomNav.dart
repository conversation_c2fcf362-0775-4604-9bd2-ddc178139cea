import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/providers/config_provicer.dart';
import 'package:tattooerp20260622/providers/saleorder_provider.dart';
import 'package:tattooerp20260622/router/sale/sale_order.dart';

import '../router/me/Me.dart';
import '../router/stock/Stock.dart';
import '../router/transfer/Transfer.dart';

class SellerBottomMav extends StatefulWidget {
  const SellerBottomMav({Key? key}) : super(key: key);

  @override
  _SellerBottomMav createState() => _SellerBottomMav();
}

class _SellerBottomMav extends State<StatefulWidget> with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    //获取配置信息
    context.read<ConfigProvider>().getConfig();
  }

  List<Widget> sellerList = [const SaleOrder(), const Stock(), Text(""), const Transfer(), const Me()];

  List<BottomNavigationBarItem> sellerNavList = const [
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/sale.png")), label: "Sale"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/stock.png")), label: "Stock"),
    BottomNavigationBarItem(icon: Icon(null, color: Color(0xffffffff)), label: ""),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/diaobo.png")), label: "Transfer"),
    BottomNavigationBarItem(icon: ImageIcon(AssetImage("images/avatar.png")), label: "Me"),
  ];

  @override
  void dispose() {
    super.dispose();
  }

  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0x00000000),
      // backgroundColor: const Color(0xfff7f9fb),
      bottomNavigationBar: BottomNavigationBar(
        fixedColor: const Color(0xffcc003f),
        unselectedItemColor: const Color(0xff013d87),
        type: BottomNavigationBarType.fixed,
        elevation: 11.0,
        unselectedFontSize: 12.0,
        selectedFontSize: 12.0,
        items: sellerNavList,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
      ),
      resizeToAvoidBottomInset: false,
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xff013d87),
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: const [ImageIcon(AssetImage("images/qrcode.png"), size: 40, color: Color(0xf0f0f0ff))]),
        onPressed: () {
          // CreateSale
          context.read<SaleOrderProvider>().createNewOrder();
          Navigator.pushNamed(context, "createSale");
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      body: IndexedStack(index: _selectedIndex, children: sellerList),
    );
  }
}

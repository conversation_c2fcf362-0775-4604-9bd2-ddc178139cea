import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tattooerp20260622/providers/config_provicer.dart';

import '../main.dart';
import '../model/User.dart';
import '../utils/storage.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final GlobalKey<ScaffoldState> _globalKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    _route();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _route() async {
    Future.delayed(const Duration(seconds: 1), () {
      LocalStorage.getString("user_token").then(
        (e) async {
          if (e is String) {
            //获取用户信息
            await LocalStorage.getString("user_info").then((e) {
              if (e == null || e == "") {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  "login",
                  (Route<dynamic> route) => false,
                );
                return;
              }
              App.adminInfo = Admin.fromJson(jsonDecode(e));
              if (App.adminInfo.store_id > 1) {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  "sellerNav",
                  (Route<dynamic> route) => false,
                );
              } else {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  "keeperNav",
                  (Route<dynamic> route) => false,
                );
              }
            });
            //获取配置信息
            context.read<ConfigProvider>().getConfig();
          } else {
            Navigator.pushNamedAndRemoveUntil(
              context,
              "login",
              (Route<dynamic> route) => false,
            );
          }
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return Scaffold(
      key: _globalKey,
      body: Container(
        color: Colors.white,
        width: width,
        height: height,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                  width: width * .7,
                  child: Image.asset("images/logo.png", height: 175)),
            ],
          ),
        ),
      ),
    );
  }
}

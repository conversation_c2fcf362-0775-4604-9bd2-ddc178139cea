import 'dart:developer';

import 'package:common_utils/common_utils.dart';
import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tattooerp20260622/common/Config.dart';
import 'package:tattooerp20260622/main.dart';
import 'package:tattooerp20260622/model/ResponseModel.dart';
import 'package:tattooerp20260622/utils/storage.dart';

class HttpManager {
  static HttpManager _instance = HttpManager._internal();
  Dio? _dio;

  static const CODE_SUCCESS = 200;
  static const CODE_TIME_OUT = -1;

  factory HttpManager() => _instance;

  HttpManager._internal() {
    if (null == _dio) {
      _dio = Dio(
        BaseOptions(
          baseUrl: Config.baseUrl,
          connectTimeout: const Duration(seconds: 60),
        ),
      );

      var cacheStore = MemCacheStore(maxSize: 10485760, maxEntrySize: 1048576);
      var cacheOptions = CacheOptions(
        store: cacheStore,
        hitCacheOnErrorExcept: [], // for offline behaviour
      );
      //添加缓存组件
      _dio?.interceptors.add(
        DioCacheInterceptor(options: cacheOptions),
      );

      //添加拦截器
      _dio?.interceptors.add(
        InterceptorsWrapper(
          onRequest:
              (RequestOptions options, RequestInterceptorHandler _rip) async {
            print(
                "\n======================================== 请求数据 ================================================");
            await LocalStorage.getString("user_token").then((e) {
              options.headers['Authorization'] = "Bearer " + e;
            });
            if ((options.headers['Authorization'] == "" ||
                options.headers['Authorization'] == null)) {
              App.navigatorKey.currentState
                  ?.pushNamedAndRemoveUntil("login", (route) => false);
            }
            print(options.headers);
            print(options.uri);
            return _rip.next(options);
            // 3.对参数进行一些处理,比如序列化处理等
          },
          onResponse: (Response response, ResponseInterceptorHandler _rip) {
            print(
                "======================= 响应数据 ===============================");
            print("code = ${response.statusCode}");
            LogUtil.e("data = ${response.data}");
            if (response.data
                    .toString()
                    .indexOf("\"code\":1012,\"msg\":\"log out\"") >=
                0) {
              EasyLoading.dismiss();
              LocalStorage.remove("user_token");
              App.navigatorKey.currentState
                  ?.pushNamedAndRemoveUntil("login", (route) => false);
              return;
            }
            try {
              return _rip.next(response);
            } catch (e) {
              Fluttertoast.showToast(
                msg: "请求出错",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                timeInSecForIosWeb: 1,
                backgroundColor: Colors.black45,
                textColor: Colors.white,
                fontSize: 15.0,
              );
              return _rip.next(response);
            }
          },
          onError: (DioError e, ErrorInterceptorHandler _eip) {
            print("================== 错误响应数据 START ======================");
            print("type = ${e.type}");
            print("message = ${e.message}");
            LogUtil.e("data = ${e.response?.data}");
            print("================== 错误响应数据 END ======================");
            //App.navigatorKey.currentState?.
            //App.navigatorKey.currentState?.pushNamedAndRemoveUntil("login", (route) => false);
            Fluttertoast.showToast(
              msg: "请求出错",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.black45,
              textColor: Colors.white,
              fontSize: 15.0,
            );
            return _eip.next(e);
          },
        ),
      );
    }
  }

  static HttpManager getInstance({String baseUrl = "", BuildContext? cxt}) {
    if (baseUrl == "") {
      return _instance._normal();
    } else {
      return _instance._baseUrl(baseUrl);
    }
  }

  //用于指定特定域名
  HttpManager _baseUrl(String baseUrl) {
    if (_dio != null) {
      _dio?.options.baseUrl = baseUrl;
    }
    return this;
  }

  //一般请求，默认域名
  HttpManager _normal() {
    if (_dio != null) {
      if (_dio?.options.baseUrl != Config.baseUrl) {
        _dio?.options.baseUrl = Config.baseUrl;
      }
    }
    return this;
  }

  ///通用的GET请求
  get(api, {params, withLoading = true, int cache = 1}) async {
    if (withLoading) EasyLoading.show(status: 'loading');
    Response? response;
    try {
      response = await _dio?.get(
        api,
        queryParameters: params,
      );
      if (withLoading) EasyLoading.dismiss();
    } on DioError catch (e) {
      if (withLoading) EasyLoading.dismiss();
      return resultError(e);
    }
    if (response?.data is DioError) {
      return resultError(response?.data['code']);
    }

    return response?.data;
  }

  ///通用的POST请求
  post(api, {params, withLoading = false, int cache = 1}) async {
    if (withLoading) EasyLoading.show(status: 'loading');
    Response? response;
    try {
      response = await _dio?.post(
        api,
        data: params,
      );
      if (withLoading) EasyLoading.dismiss();
    } on DioError catch (e) {
      if (withLoading) EasyLoading.dismiss();
      return resultError(e);
    }

    if (response?.data is DioError) {
      log(response?.data['code']);
      return resultError(response?.data['code']);
    }

    return response?.data;
  }

  Future uploadImg(url, data) async {
    Dio uploadDio = Dio();
    uploadDio.options.contentType = 'multipart/form-data';
    try {
      String token = "none";
      await LocalStorage.getString("user_token").then((e) {
        token = e;
      });
      Response response = await uploadDio.post(
        Config.baseUrl + url,
        // Config.baseUrl + "file/iconUploadComproess",
        options: Options(
          headers: {
            "token": token,
            Headers.acceptHeader: "*/*",
            Headers.contentTypeHeader:
                "multipart/form-data;boundary=------WebKitFormBoundaryuwYcfA2AIgxqIxA0",
          },
        ),
        data: data,
      );
      print(response.data);
      return response.data;
    } catch (e) {
      print(e.toString());
    }
  }
}

ResponseModel resultError(DioError e) {
  Response? errorResponse;
  if (e.response != null) {
    errorResponse = e.response;
  } else {}
  if (e.type == DioErrorType.connectionTimeout ||
      e.type == DioErrorType.receiveTimeout) {
    errorResponse?.statusCode = 1200;
  }
  return new ResponseModel(code: 200, data: '', msg: 'failed', success: false);
}

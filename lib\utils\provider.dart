// import 'package:flutter/material.dart';

// // 必须被重写的抽象接口
// abstract class BlocBase {
//   void dispose();
// }

// // Provider
// class BlocProvider<T extends BlocBase> extends StatefulWidget {
//   final T bloc;
//   final Widget child;

//   BlocProvider({
//     Key key,
//     @required this.child,
//     @required this.bloc,
//   }): super(key: key);

//   static T of<T extends BlocBase>(BuildContext context){

//     final type = _typeOf<BlocProvider<T>>();
//     BlocProvider<T> provider = context.ancestorWidgetOfExactType(type);
//     return provider.bloc;
//   }

//   static Type _typeOf<T>() => T;

//   @override
//   _BlocProviderState<T> createState() => _BlocProviderState<T>();

// }

// class _BlocProviderState<T> extends State<BlocProvider<BlocBase>>{
//   @override
//   void dispose(){
//     widget.bloc.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context){
//     return widget.child;
//   }
// }
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class Utils {
  static transTime(String origin_time) {
    try {
      DateTime tempTime = DateTime.parse(origin_time);
      return buLing(tempTime.day.toString()) +
          "/" +
          buLing(tempTime.month.toString()) +
          "/" +
          tempTime.year.toString() +
          " " +
          buLing(tempTime.hour.toString()) +
          ":" +
          buLing(tempTime.minute.toString());
    } catch (e) {
      return origin_time;
    }
  }

  static buLing(String _num) {
    if (_num.length == 1) {
      _num = "0" + _num;
    }
    return _num;
  }

  static Widget orderStatus(order_status, status_name) {
    if (order_status.toString() == "0") {
      return Text(
        status_name,
        style: const TextStyle(color: Colors.grey),
      );
    }
    if (order_status.toString() == "1") {
      return Text(
        status_name,
        style: const TextStyle(color: Colors.green),
      );
    }
    if (order_status.toString() == "2") {
      return Text(
        status_name,
        style: const TextStyle(color: Colors.red),
      );
    }
    return Text(status_name);
  }
}

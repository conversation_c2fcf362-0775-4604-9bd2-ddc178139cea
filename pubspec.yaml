name: tattooerp20260622
description: "tattooerp20250622"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # share: ^2.0.4
  permission_handler: ^11.3.1
  mek_stripe_terminal: ^4.0.2
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  stripe:
    git:
      url: https://github.com/enyo/stripe-dart
  barcode_newland_flutter: ^1.0.1
  cupertino_icons: ^1.0.8
  common_utils: ^2.1.0
  badges: ^3.1.2
  adaptive_dialog: ^2.4.2
  # flutter_app_badger: ^1.5.0
  font_awesome_flutter: ^10.8.0
  dio: ^5.5.1
  dio_cache_interceptor: ^3.2.7
  fluttertoast: ^8.2.12
  provider: ^6.1.5
  shared_preferences: ^2.2.0
  path_provider: ^2.1.5
  #image_picker: ^0.8.5
  #image_crop: ^0.4.0
  # flutter_tag_layout: ^0.0.3
  flutter_picker: ^2.0.3
  flutter_easyloading: ^3.0.3 #loading
  # flutter_custom_dialog: ^1.2.0 #自定义弹窗
  #photo_view: ^0.14.0 #相册预览
  #flutter_rating_bar: ^4.0.0 #星星评级
  flutter_native_splash: ^2.1.6 #生成闪屏
  flutter_screenutil: ^5.9.3
  pull_to_refresh_flutter3:  ^2.0.2 #上滑 下拉 加载 刷新
  get: ^4.6.5
  #amap_flutter_location: ^3.0.0
  cached_network_image: ^3.2.0
  json_annotation: ^4.7.0
  dotted_border: ^2.0.0+3
  #connectivity_plus: ^3.0.3
  intl: ^0.18.0
  icons_launcher: ^2.0.1 #生成app icon
  calendar_date_picker2: 0.4.5
  date_format: ^2.0.7

dev_dependencies:
  build_runner: ^2.3.2
  json_serializable: ^6.5.4
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - images/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
icons_launcher:
  image_path: 'images/tattoo.png'
  platforms:
    android:
      enable: true
    ios:
      enable: true